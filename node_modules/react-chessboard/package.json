{"name": "react-chessboard", "version": "5.0.1", "description": "The React Chessboard Library", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "files": ["dist"], "packageManager": "pnpm@9.4.0", "engines": {"node": ">=20.11.0", "pnpm": ">=9.4.0"}, "repository": {"type": "git", "url": "git+https://github.com/Clariity/react-chessboard.git"}, "bugs": {"url": "https://github.com/Clariity/react-chessboard/issues"}, "homepage": "https://github.com/Clariity/react-chessboard#readme", "scripts": {"build": "rimraf dist && rollup -c", "test:static": "tsc && eslint src docs && prettier --check src docs", "format": "eslint --fix src docs && prettier --write src docs", "prettier:fix": "prettier --write src docs", "commitlint": "commitlint --edit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare": "husky"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0"}, "devDependencies": {"@chromatic-com/storybook": "3", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.26.0", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@storybook/addon-docs": "^8.6.8", "@storybook/addon-essentials": "8.6.8", "@storybook/addon-interactions": "8.6.8", "@storybook/blocks": "8.6.8", "@storybook/manager-api": "8.6.8", "@storybook/react": "8.6.8", "@storybook/react-vite": "8.6.8", "@storybook/test": "8.6.8", "@storybook/theming": "8.6.8", "@types/node": "^22.13.11", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "chess.js": "^1.2.0", "conventional-changelog-conventionalcommits": "^9.0.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "globals": "^16.1.0", "husky": "^9.1.7", "prettier": "^3.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "remark-gfm": "^4.0.1", "rimraf": "^6.0.1", "rollup": "^4.42.0", "rollup-plugin-typescript2": "^0.36.0", "semantic-release": "^24.2.5", "storybook": "8.6.8", "storybook-addon-deep-controls": "^0.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.32.0"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}