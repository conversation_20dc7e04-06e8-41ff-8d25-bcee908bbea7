export declare function defaultBoardStyle(chessboardColumns: number): React.CSSProperties;
export declare const defaultSquareStyle: React.CSSProperties;
export declare const defaultDarkSquareStyle: React.CSSProperties;
export declare const defaultLightSquareStyle: React.CSSProperties;
export declare const defaultDropSquareStyle: React.CSSProperties;
export declare const defaultDarkSquareNotationStyle: React.CSSProperties;
export declare const defaultLightSquareNotationStyle: React.CSSProperties;
export declare const defaultAlphaNotationStyle: React.CSSProperties;
export declare const defaultNumericNotationStyle: React.CSSProperties;
export declare const defaultDraggingPieceStyle: React.CSSProperties;
export declare const defaultDraggingPieceGhostStyle: React.CSSProperties;
export declare const defaultArrowOptions: {
    color: string;
    secondaryColor: string;
    tertiaryColor: string;
    arrowLengthReducerDenominator: number;
    sameTargetArrowLengthReducerDenominator: number;
    arrowWidthDenominator: number;
    activeArrowWidthMultiplier: number;
    opacity: number;
    activeOpacity: number;
};
