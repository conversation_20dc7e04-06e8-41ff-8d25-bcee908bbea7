import type { SquareDataType, PositionDataType } from './types';
export declare function generateBoard(noOfRows: number, noOfColumns: number, boardOrientation: 'white' | 'black'): SquareDataType[][];
export declare function rowIndexToChessRow(row: number, noOfRows: number, boardOrientation: 'white' | 'black'): string;
export declare function columnIndexToChessColumn(column: number, noOfColumns: number, boardOrientation: 'white' | 'black'): string;
export declare function chessColumnToColumnIndex(column: string, noOfColumns: number, boardOrientation: 'white' | 'black'): number;
export declare function chessRowToRowIndex(row: string, noOfRows: number, boardOrientation: 'white' | 'black'): number;
export declare function fenStringToPositionObject(fen: string, noOfRows: number, noOfColumns: number): PositionDataType;
/**
 * Return an object with the pieces that have moved from the old position to the new position.
 * The keys are the source square names (e.g. "e2") and the values are the new square positions (e.g. "e4"), indicating that the piece in square "e2" has moved to square "e4".
 */
export declare function getPositionUpdates(oldPosition: PositionDataType, newPosition: PositionDataType, noOfColumns: number, boardOrientation: 'white' | 'black'): {
    [square: string]: string;
};
/**
 * Retrieves the coordinates at the centre of the requested square, relative to the top left of the board (0, 0).
 */
export declare function getRelativeCoords(boardOrientation: 'white' | 'black', boardWidth: number, chessboardColumns: number, chessboardRows: number, square: string): {
    x: number;
    y: number;
};
