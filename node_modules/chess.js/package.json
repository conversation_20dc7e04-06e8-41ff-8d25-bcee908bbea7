{"name": "chess.js", "version": "1.4.0", "license": "BSD-2-<PERSON><PERSON>", "main": "dist/cjs/chess.js", "module": "dist/esm/chess.js", "types": "dist/types/chess.d.ts", "homepage": "https://github.com/jhlywa/chess.js", "author": "<PERSON> <<EMAIL>>", "scripts": {"prepare": "npm run build", "build": "npm run parser && tsc --noEmit && rollup -c", "check": "npm run format:check && npm run lint && npm run test && npm run build", "clean": "rm -rf ./dist; rm -f src/pgn.js src/pgn.d.ts", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint src/ --ext .ts", "parser": "peggy -c peggy.config.mjs", "test": "vitest", "test:coverage": "vitest --coverage", "bench": "tsx benchmarks/bench.ts"}, "repository": {"type": "git", "url": "https://github.com/jhlywa/chess.js"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-typescript": "^12.1.2", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.17.0", "@vitest/coverage-v8": "^3.2.2", "eslint": "^8.12.0", "peggy": "^4.2.0", "prettier": "^3.1.0", "rollup": "^4.41.1", "rollup-plugin-dts": "^6.2.1", "tinybench": "^4.0.1", "tslib": "^2.8.1", "tsx": "^4.19.4", "typescript": "^4.6.3", "vitest": "^3.2.2"}}