{"version": 3, "file": "chess.js", "sources": ["../../src/pgn.js", "../../../src/chess.ts"], "sourcesContent": ["// @generated by Peggy 4.2.0.\n//\n// https://peggyjs.org/\n\n\n\n  function rootNode(comment) {\n  \treturn comment !== null ? { comment, variations: [] } : { variations: []}\n  }\n\n  function node(move, suffix, nag, comment, variations) {\n  \tconst node = { move, variations }\n\n    if (suffix) {\n    \tnode.suffix = suffix\n    }\n\n    if (nag) {\n    \tnode.nag = nag\n    }\n\n    if (comment !== null) {\n    \tnode.comment = comment\n    }\n\n    return node\n  }\n\n  function lineToTree(...nodes) {\n  \tconst [root, ...rest] = nodes;\n\n    let parent = root\n\n    for (const child of rest) {\n    \tif (child !== null) {\n        \tparent.variations = [child, ...child.variations]\n            child.variations = []\n            parent = child\n        }\n    }\n\n  \treturn root\n  }\n\n  function pgn(headers, game) {\n  \tif (game.marker && game.marker.comment) {\n    \tlet node = game.root\n        while (true) {\n        \tconst next = node.variations[0]\n            if (!next) {\n            \tnode.comment = game.marker.comment\n            \tbreak\n            }\n            node = next\n        }\n    }\n\n  \treturn {\n    \theaders,\n        root: game.root,\n        result: (game.marker && game.marker.result) ?? undefined\n    }\n  }\n\nfunction peg$subclass(child, parent) {\n  function C() { this.constructor = child; }\n  C.prototype = parent.prototype;\n  child.prototype = new C();\n}\n\nfunction peg$SyntaxError(message, expected, found, location) {\n  var self = Error.call(this, message);\n  // istanbul ignore next Check is a necessary evil to support older environments\n  if (Object.setPrototypeOf) {\n    Object.setPrototypeOf(self, peg$SyntaxError.prototype);\n  }\n  self.expected = expected;\n  self.found = found;\n  self.location = location;\n  self.name = \"SyntaxError\";\n  return self;\n}\n\npeg$subclass(peg$SyntaxError, Error);\n\nfunction peg$padEnd(str, targetLength, padString) {\n  padString = padString || \" \";\n  if (str.length > targetLength) { return str; }\n  targetLength -= str.length;\n  padString += padString.repeat(targetLength);\n  return str + padString.slice(0, targetLength);\n}\n\npeg$SyntaxError.prototype.format = function(sources) {\n  var str = \"Error: \" + this.message;\n  if (this.location) {\n    var src = null;\n    var k;\n    for (k = 0; k < sources.length; k++) {\n      if (sources[k].source === this.location.source) {\n        src = sources[k].text.split(/\\r\\n|\\n|\\r/g);\n        break;\n      }\n    }\n    var s = this.location.start;\n    var offset_s = (this.location.source && (typeof this.location.source.offset === \"function\"))\n      ? this.location.source.offset(s)\n      : s;\n    var loc = this.location.source + \":\" + offset_s.line + \":\" + offset_s.column;\n    if (src) {\n      var e = this.location.end;\n      var filler = peg$padEnd(\"\", offset_s.line.toString().length, ' ');\n      var line = src[s.line - 1];\n      var last = s.line === e.line ? e.column : line.length + 1;\n      var hatLen = (last - s.column) || 1;\n      str += \"\\n --> \" + loc + \"\\n\"\n          + filler + \" |\\n\"\n          + offset_s.line + \" | \" + line + \"\\n\"\n          + filler + \" | \" + peg$padEnd(\"\", s.column - 1, ' ')\n          + peg$padEnd(\"\", hatLen, \"^\");\n    } else {\n      str += \"\\n at \" + loc;\n    }\n  }\n  return str;\n};\n\npeg$SyntaxError.buildMessage = function(expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n    literal: function(expectation) {\n      return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n    },\n\n    class: function(expectation) {\n      var escapedParts = expectation.parts.map(function(part) {\n        return Array.isArray(part)\n          ? classEscape(part[0]) + \"-\" + classEscape(part[1])\n          : classEscape(part);\n      });\n\n      return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts.join(\"\") + \"]\";\n    },\n\n    any: function() {\n      return \"any character\";\n    },\n\n    end: function() {\n      return \"end of input\";\n    },\n\n    other: function(expectation) {\n      return expectation.description;\n    }\n  };\n\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n\n  function literalEscape(s) {\n    return s\n      .replace(/\\\\/g, \"\\\\\\\\\")\n      .replace(/\"/g,  \"\\\\\\\"\")\n      .replace(/\\0/g, \"\\\\0\")\n      .replace(/\\t/g, \"\\\\t\")\n      .replace(/\\n/g, \"\\\\n\")\n      .replace(/\\r/g, \"\\\\r\")\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return \"\\\\x0\" + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return \"\\\\x\"  + hex(ch); });\n  }\n\n  function classEscape(s) {\n    return s\n      .replace(/\\\\/g, \"\\\\\\\\\")\n      .replace(/\\]/g, \"\\\\]\")\n      .replace(/\\^/g, \"\\\\^\")\n      .replace(/-/g,  \"\\\\-\")\n      .replace(/\\0/g, \"\\\\0\")\n      .replace(/\\t/g, \"\\\\t\")\n      .replace(/\\n/g, \"\\\\n\")\n      .replace(/\\r/g, \"\\\\r\")\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return \"\\\\x0\" + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return \"\\\\x\"  + hex(ch); });\n  }\n\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n\n  function describeExpected(expected) {\n    var descriptions = expected.map(describeExpectation);\n    var i, j;\n\n    descriptions.sort();\n\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n\n      case 2:\n        return descriptions[0] + \" or \" + descriptions[1];\n\n      default:\n        return descriptions.slice(0, -1).join(\", \")\n          + \", or \"\n          + descriptions[descriptions.length - 1];\n    }\n  }\n\n  function describeFound(found) {\n    return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n  }\n\n  return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n};\n\nfunction peg$parse(input, options) {\n  options = options !== undefined ? options : {};\n\n  var peg$FAILED = {};\n  var peg$source = options.grammarSource;\n\n  var peg$startRuleFunctions = { pgn: peg$parsepgn };\n  var peg$startRuleFunction = peg$parsepgn;\n\n  var peg$c0 = \"[\";\n  var peg$c1 = \"\\\"\";\n  var peg$c2 = \"]\";\n  var peg$c3 = \".\";\n  var peg$c4 = \"O-O-O\";\n  var peg$c5 = \"O-O\";\n  var peg$c6 = \"0-0-0\";\n  var peg$c7 = \"0-0\";\n  var peg$c8 = \"$\";\n  var peg$c9 = \"{\";\n  var peg$c10 = \"}\";\n  var peg$c11 = \";\";\n  var peg$c12 = \"(\";\n  var peg$c13 = \")\";\n  var peg$c14 = \"1-0\";\n  var peg$c15 = \"0-1\";\n  var peg$c16 = \"1/2-1/2\";\n  var peg$c17 = \"*\";\n\n  var peg$r0 = /^[a-zA-Z]/;\n  var peg$r1 = /^[^\"]/;\n  var peg$r2 = /^[0-9]/;\n  var peg$r3 = /^[.]/;\n  var peg$r4 = /^[a-zA-Z1-8\\-=]/;\n  var peg$r5 = /^[+#]/;\n  var peg$r6 = /^[!?]/;\n  var peg$r7 = /^[^}]/;\n  var peg$r8 = /^[^\\r\\n]/;\n  var peg$r9 = /^[ \\t\\r\\n]/;\n\n  var peg$e0 = peg$otherExpectation(\"tag pair\");\n  var peg$e1 = peg$literalExpectation(\"[\", false);\n  var peg$e2 = peg$literalExpectation(\"\\\"\", false);\n  var peg$e3 = peg$literalExpectation(\"]\", false);\n  var peg$e4 = peg$otherExpectation(\"tag name\");\n  var peg$e5 = peg$classExpectation([[\"a\", \"z\"], [\"A\", \"Z\"]], false, false);\n  var peg$e6 = peg$otherExpectation(\"tag value\");\n  var peg$e7 = peg$classExpectation([\"\\\"\"], true, false);\n  var peg$e8 = peg$otherExpectation(\"move number\");\n  var peg$e9 = peg$classExpectation([[\"0\", \"9\"]], false, false);\n  var peg$e10 = peg$literalExpectation(\".\", false);\n  var peg$e11 = peg$classExpectation([\".\"], false, false);\n  var peg$e12 = peg$otherExpectation(\"standard algebraic notation\");\n  var peg$e13 = peg$literalExpectation(\"O-O-O\", false);\n  var peg$e14 = peg$literalExpectation(\"O-O\", false);\n  var peg$e15 = peg$literalExpectation(\"0-0-0\", false);\n  var peg$e16 = peg$literalExpectation(\"0-0\", false);\n  var peg$e17 = peg$classExpectation([[\"a\", \"z\"], [\"A\", \"Z\"], [\"1\", \"8\"], \"-\", \"=\"], false, false);\n  var peg$e18 = peg$classExpectation([\"+\", \"#\"], false, false);\n  var peg$e19 = peg$otherExpectation(\"suffix annotation\");\n  var peg$e20 = peg$classExpectation([\"!\", \"?\"], false, false);\n  var peg$e21 = peg$otherExpectation(\"NAG\");\n  var peg$e22 = peg$literalExpectation(\"$\", false);\n  var peg$e23 = peg$otherExpectation(\"brace comment\");\n  var peg$e24 = peg$literalExpectation(\"{\", false);\n  var peg$e25 = peg$classExpectation([\"}\"], true, false);\n  var peg$e26 = peg$literalExpectation(\"}\", false);\n  var peg$e27 = peg$otherExpectation(\"rest of line comment\");\n  var peg$e28 = peg$literalExpectation(\";\", false);\n  var peg$e29 = peg$classExpectation([\"\\r\", \"\\n\"], true, false);\n  var peg$e30 = peg$otherExpectation(\"variation\");\n  var peg$e31 = peg$literalExpectation(\"(\", false);\n  var peg$e32 = peg$literalExpectation(\")\", false);\n  var peg$e33 = peg$otherExpectation(\"game termination marker\");\n  var peg$e34 = peg$literalExpectation(\"1-0\", false);\n  var peg$e35 = peg$literalExpectation(\"0-1\", false);\n  var peg$e36 = peg$literalExpectation(\"1/2-1/2\", false);\n  var peg$e37 = peg$literalExpectation(\"*\", false);\n  var peg$e38 = peg$otherExpectation(\"whitespace\");\n  var peg$e39 = peg$classExpectation([\" \", \"\\t\", \"\\r\", \"\\n\"], false, false);\n\n  var peg$f0 = function(headers, game) { return pgn(headers, game) };\n  var peg$f1 = function(tagPairs) { return Object.fromEntries(tagPairs) };\n  var peg$f2 = function(tagName, tagValue) { return [tagName, tagValue] };\n  var peg$f3 = function(root, marker) { return { root, marker} };\n  var peg$f4 = function(comment, moves) { return lineToTree(rootNode(comment), ...moves.flat()) };\n  var peg$f5 = function(san, suffix, nag, comment, variations) { return node(san, suffix, nag, comment, variations) };\n  var peg$f6 = function(nag) { return nag };\n  var peg$f7 = function(comment) { return comment.replace(/[\\r\\n]+/g, \" \") };\n  var peg$f8 = function(comment) { return comment.trim() };\n  var peg$f9 = function(line) { return line };\n  var peg$f10 = function(result, comment) { return { result, comment } };\n  var peg$currPos = options.peg$currPos | 0;\n  var peg$savedPos = peg$currPos;\n  var peg$posDetailsCache = [{ line: 1, column: 1 }];\n  var peg$maxFailPos = peg$currPos;\n  var peg$maxFailExpected = options.peg$maxFailExpected || [];\n  var peg$silentFails = options.peg$silentFails | 0;\n\n  var peg$result;\n\n  if (options.startRule) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n    }\n\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n\n  function text() {\n    return input.substring(peg$savedPos, peg$currPos);\n  }\n\n  function offset() {\n    return peg$savedPos;\n  }\n\n  function range() {\n    return {\n      source: peg$source,\n      start: peg$savedPos,\n      end: peg$currPos\n    };\n  }\n\n  function location() {\n    return peg$computeLocation(peg$savedPos, peg$currPos);\n  }\n\n  function expected(description, location) {\n    location = location !== undefined\n      ? location\n      : peg$computeLocation(peg$savedPos, peg$currPos);\n\n    throw peg$buildStructuredError(\n      [peg$otherExpectation(description)],\n      input.substring(peg$savedPos, peg$currPos),\n      location\n    );\n  }\n\n  function error(message, location) {\n    location = location !== undefined\n      ? location\n      : peg$computeLocation(peg$savedPos, peg$currPos);\n\n    throw peg$buildSimpleError(message, location);\n  }\n\n  function peg$literalExpectation(text, ignoreCase) {\n    return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n  }\n\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n  }\n\n  function peg$anyExpectation() {\n    return { type: \"any\" };\n  }\n\n  function peg$endExpectation() {\n    return { type: \"end\" };\n  }\n\n  function peg$otherExpectation(description) {\n    return { type: \"other\", description: description };\n  }\n\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos];\n    var p;\n\n    if (details) {\n      return details;\n    } else {\n      if (pos >= peg$posDetailsCache.length) {\n        p = peg$posDetailsCache.length - 1;\n      } else {\n        p = pos;\n        while (!peg$posDetailsCache[--p]) {}\n      }\n\n      details = peg$posDetailsCache[p];\n      details = {\n        line: details.line,\n        column: details.column\n      };\n\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n\n        p++;\n      }\n\n      peg$posDetailsCache[pos] = details;\n\n      return details;\n    }\n  }\n\n  function peg$computeLocation(startPos, endPos, offset) {\n    var startPosDetails = peg$computePosDetails(startPos);\n    var endPosDetails = peg$computePosDetails(endPos);\n\n    var res = {\n      source: peg$source,\n      start: {\n        offset: startPos,\n        line: startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line: endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n    if (offset && peg$source && (typeof peg$source.offset === \"function\")) {\n      res.start = peg$source.offset(res.start);\n      res.end = peg$source.offset(res.end);\n    }\n    return res;\n  }\n\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) { return; }\n\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n\n    peg$maxFailExpected.push(expected);\n  }\n\n  function peg$buildSimpleError(message, location) {\n    return new peg$SyntaxError(message, null, null, location);\n  }\n\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(\n      peg$SyntaxError.buildMessage(expected, found),\n      expected,\n      found,\n      location\n    );\n  }\n\n  function peg$parsepgn() {\n    var s0, s1, s2;\n\n    s0 = peg$currPos;\n    s1 = peg$parsetagPairSection();\n    s2 = peg$parsemoveTextSection();\n    peg$savedPos = s0;\n    s0 = peg$f0(s1, s2);\n\n    return s0;\n  }\n\n  function peg$parsetagPairSection() {\n    var s0, s1, s2;\n\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsetagPair();\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = peg$parsetagPair();\n    }\n    s2 = peg$parse_();\n    peg$savedPos = s0;\n    s0 = peg$f1(s1);\n\n    return s0;\n  }\n\n  function peg$parsetagPair() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.charCodeAt(peg$currPos) === 91) {\n      s2 = peg$c0;\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e1); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = peg$parsetagName();\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 34) {\n          s6 = peg$c1;\n          peg$currPos++;\n        } else {\n          s6 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e2); }\n        }\n        if (s6 !== peg$FAILED) {\n          s7 = peg$parsetagValue();\n          if (input.charCodeAt(peg$currPos) === 34) {\n            s8 = peg$c1;\n            peg$currPos++;\n          } else {\n            s8 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$e2); }\n          }\n          if (s8 !== peg$FAILED) {\n            s9 = peg$parse_();\n            if (input.charCodeAt(peg$currPos) === 93) {\n              s10 = peg$c2;\n              peg$currPos++;\n            } else {\n              s10 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$e3); }\n            }\n            if (s10 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s0 = peg$f2(s4, s7);\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e0); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsetagName() {\n    var s0, s1, s2;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = input.charAt(peg$currPos);\n    if (peg$r0.test(s2)) {\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e5); }\n    }\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = input.charAt(peg$currPos);\n        if (peg$r0.test(s2)) {\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e5); }\n        }\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s0 = input.substring(s0, peg$currPos);\n    } else {\n      s0 = s1;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e4); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsetagValue() {\n    var s0, s1, s2;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = input.charAt(peg$currPos);\n    if (peg$r1.test(s2)) {\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e7); }\n    }\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = input.charAt(peg$currPos);\n      if (peg$r1.test(s2)) {\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e7); }\n      }\n    }\n    s0 = input.substring(s0, peg$currPos);\n    peg$silentFails--;\n    s1 = peg$FAILED;\n    if (peg$silentFails === 0) { peg$fail(peg$e6); }\n\n    return s0;\n  }\n\n  function peg$parsemoveTextSection() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = peg$parseline();\n    s2 = peg$parse_();\n    s3 = peg$parsegameTerminationMarker();\n    if (s3 === peg$FAILED) {\n      s3 = null;\n    }\n    s4 = peg$parse_();\n    peg$savedPos = s0;\n    s0 = peg$f3(s1, s3);\n\n    return s0;\n  }\n\n  function peg$parseline() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecomment();\n    if (s1 === peg$FAILED) {\n      s1 = null;\n    }\n    s2 = [];\n    s3 = peg$parsemove();\n    while (s3 !== peg$FAILED) {\n      s2.push(s3);\n      s3 = peg$parsemove();\n    }\n    peg$savedPos = s0;\n    s0 = peg$f4(s1, s2);\n\n    return s0;\n  }\n\n  function peg$parsemove() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10;\n\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    s2 = peg$parsemoveNumber();\n    if (s2 === peg$FAILED) {\n      s2 = null;\n    }\n    s3 = peg$parse_();\n    s4 = peg$parsesan();\n    if (s4 !== peg$FAILED) {\n      s5 = peg$parsesuffixAnnotation();\n      if (s5 === peg$FAILED) {\n        s5 = null;\n      }\n      s6 = [];\n      s7 = peg$parsenag();\n      while (s7 !== peg$FAILED) {\n        s6.push(s7);\n        s7 = peg$parsenag();\n      }\n      s7 = peg$parse_();\n      s8 = peg$parsecomment();\n      if (s8 === peg$FAILED) {\n        s8 = null;\n      }\n      s9 = [];\n      s10 = peg$parsevariation();\n      while (s10 !== peg$FAILED) {\n        s9.push(s10);\n        s10 = peg$parsevariation();\n      }\n      peg$savedPos = s0;\n      s0 = peg$f5(s4, s5, s6, s8, s9);\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsemoveNumber() {\n    var s0, s1, s2, s3, s4, s5;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = input.charAt(peg$currPos);\n    if (peg$r2.test(s2)) {\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e9); }\n    }\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = input.charAt(peg$currPos);\n      if (peg$r2.test(s2)) {\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e9); }\n      }\n    }\n    if (input.charCodeAt(peg$currPos) === 46) {\n      s2 = peg$c3;\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e10); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = [];\n      s5 = input.charAt(peg$currPos);\n      if (peg$r3.test(s5)) {\n        peg$currPos++;\n      } else {\n        s5 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e11); }\n      }\n      while (s5 !== peg$FAILED) {\n        s4.push(s5);\n        s5 = input.charAt(peg$currPos);\n        if (peg$r3.test(s5)) {\n          peg$currPos++;\n        } else {\n          s5 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e11); }\n        }\n      }\n      s1 = [s1, s2, s3, s4];\n      s0 = s1;\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e8); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsesan() {\n    var s0, s1, s2, s3, s4, s5;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c4) {\n      s2 = peg$c4;\n      peg$currPos += 5;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e13); }\n    }\n    if (s2 === peg$FAILED) {\n      if (input.substr(peg$currPos, 3) === peg$c5) {\n        s2 = peg$c5;\n        peg$currPos += 3;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e14); }\n      }\n      if (s2 === peg$FAILED) {\n        if (input.substr(peg$currPos, 5) === peg$c6) {\n          s2 = peg$c6;\n          peg$currPos += 5;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e15); }\n        }\n        if (s2 === peg$FAILED) {\n          if (input.substr(peg$currPos, 3) === peg$c7) {\n            s2 = peg$c7;\n            peg$currPos += 3;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$e16); }\n          }\n          if (s2 === peg$FAILED) {\n            s2 = peg$currPos;\n            s3 = input.charAt(peg$currPos);\n            if (peg$r0.test(s3)) {\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$e5); }\n            }\n            if (s3 !== peg$FAILED) {\n              s4 = [];\n              s5 = input.charAt(peg$currPos);\n              if (peg$r4.test(s5)) {\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$e17); }\n              }\n              if (s5 !== peg$FAILED) {\n                while (s5 !== peg$FAILED) {\n                  s4.push(s5);\n                  s5 = input.charAt(peg$currPos);\n                  if (peg$r4.test(s5)) {\n                    peg$currPos++;\n                  } else {\n                    s5 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$e17); }\n                  }\n                }\n              } else {\n                s4 = peg$FAILED;\n              }\n              if (s4 !== peg$FAILED) {\n                s3 = [s3, s4];\n                s2 = s3;\n              } else {\n                peg$currPos = s2;\n                s2 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s2;\n              s2 = peg$FAILED;\n            }\n          }\n        }\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = input.charAt(peg$currPos);\n      if (peg$r5.test(s3)) {\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e18); }\n      }\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      s2 = [s2, s3];\n      s1 = s2;\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s0 = input.substring(s0, peg$currPos);\n    } else {\n      s0 = s1;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e12); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsesuffixAnnotation() {\n    var s0, s1, s2;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = input.charAt(peg$currPos);\n    if (peg$r6.test(s2)) {\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e20); }\n    }\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      if (s1.length >= 2) {\n        s2 = peg$FAILED;\n      } else {\n        s2 = input.charAt(peg$currPos);\n        if (peg$r6.test(s2)) {\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e20); }\n        }\n      }\n    }\n    if (s1.length < 1) {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    } else {\n      s0 = s1;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e19); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsenag() {\n    var s0, s1, s2, s3, s4, s5;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.charCodeAt(peg$currPos) === 36) {\n      s2 = peg$c8;\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e22); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$currPos;\n      s4 = [];\n      s5 = input.charAt(peg$currPos);\n      if (peg$r2.test(s5)) {\n        peg$currPos++;\n      } else {\n        s5 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e9); }\n      }\n      if (s5 !== peg$FAILED) {\n        while (s5 !== peg$FAILED) {\n          s4.push(s5);\n          s5 = input.charAt(peg$currPos);\n          if (peg$r2.test(s5)) {\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$e9); }\n          }\n        }\n      } else {\n        s4 = peg$FAILED;\n      }\n      if (s4 !== peg$FAILED) {\n        s3 = input.substring(s3, peg$currPos);\n      } else {\n        s3 = s4;\n      }\n      if (s3 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s0 = peg$f6(s3);\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e21); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsecomment() {\n    var s0;\n\n    s0 = peg$parsebraceComment();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parserestOfLineComment();\n    }\n\n    return s0;\n  }\n\n  function peg$parsebraceComment() {\n    var s0, s1, s2, s3, s4;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    if (input.charCodeAt(peg$currPos) === 123) {\n      s1 = peg$c9;\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e24); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$currPos;\n      s3 = [];\n      s4 = input.charAt(peg$currPos);\n      if (peg$r7.test(s4)) {\n        peg$currPos++;\n      } else {\n        s4 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e25); }\n      }\n      while (s4 !== peg$FAILED) {\n        s3.push(s4);\n        s4 = input.charAt(peg$currPos);\n        if (peg$r7.test(s4)) {\n          peg$currPos++;\n        } else {\n          s4 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e25); }\n        }\n      }\n      s2 = input.substring(s2, peg$currPos);\n      if (input.charCodeAt(peg$currPos) === 125) {\n        s3 = peg$c10;\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e26); }\n      }\n      if (s3 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s0 = peg$f7(s2);\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e23); }\n    }\n\n    return s0;\n  }\n\n  function peg$parserestOfLineComment() {\n    var s0, s1, s2, s3, s4;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    if (input.charCodeAt(peg$currPos) === 59) {\n      s1 = peg$c11;\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e28); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$currPos;\n      s3 = [];\n      s4 = input.charAt(peg$currPos);\n      if (peg$r8.test(s4)) {\n        peg$currPos++;\n      } else {\n        s4 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e29); }\n      }\n      while (s4 !== peg$FAILED) {\n        s3.push(s4);\n        s4 = input.charAt(peg$currPos);\n        if (peg$r8.test(s4)) {\n          peg$currPos++;\n        } else {\n          s4 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e29); }\n        }\n      }\n      s2 = input.substring(s2, peg$currPos);\n      peg$savedPos = s0;\n      s0 = peg$f8(s2);\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e27); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsevariation() {\n    var s0, s1, s2, s3, s4, s5;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parse_();\n    if (input.charCodeAt(peg$currPos) === 40) {\n      s2 = peg$c12;\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e31); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parseline();\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 41) {\n          s5 = peg$c13;\n          peg$currPos++;\n        } else {\n          s5 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e32); }\n        }\n        if (s5 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s0 = peg$f9(s3);\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e30); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsegameTerminationMarker() {\n    var s0, s1, s2, s3;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 3) === peg$c14) {\n      s1 = peg$c14;\n      peg$currPos += 3;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e34); }\n    }\n    if (s1 === peg$FAILED) {\n      if (input.substr(peg$currPos, 3) === peg$c15) {\n        s1 = peg$c15;\n        peg$currPos += 3;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e35); }\n      }\n      if (s1 === peg$FAILED) {\n        if (input.substr(peg$currPos, 7) === peg$c16) {\n          s1 = peg$c16;\n          peg$currPos += 7;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e36); }\n        }\n        if (s1 === peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 42) {\n            s1 = peg$c17;\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$e37); }\n          }\n        }\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parse_();\n      s3 = peg$parsecomment();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      peg$savedPos = s0;\n      s0 = peg$f10(s1, s3);\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e33); }\n    }\n\n    return s0;\n  }\n\n  function peg$parse_() {\n    var s0, s1;\n\n    peg$silentFails++;\n    s0 = [];\n    s1 = input.charAt(peg$currPos);\n    if (peg$r9.test(s1)) {\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e39); }\n    }\n    while (s1 !== peg$FAILED) {\n      s0.push(s1);\n      s1 = input.charAt(peg$currPos);\n      if (peg$r9.test(s1)) {\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e39); }\n      }\n    }\n    peg$silentFails--;\n    s1 = peg$FAILED;\n    if (peg$silentFails === 0) { peg$fail(peg$e38); }\n\n    return s0;\n  }\n\n  peg$result = peg$startRuleFunction();\n\n  if (options.peg$library) {\n    return /** @type {any} */ ({\n      peg$result,\n      peg$currPos,\n      peg$FAILED,\n      peg$maxFailExpected,\n      peg$maxFailPos\n    });\n  }\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n\n    throw peg$buildStructuredError(\n      peg$maxFailExpected,\n      peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n      peg$maxFailPos < input.length\n        ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n        : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n    );\n  }\n}\n\nconst peg$allowedStartRules = [\n  \"pgn\"\n];\n\nexport {\n  peg$allowedStartRules as StartRules,\n  peg$SyntaxError as SyntaxError,\n  peg$parse as parse\n};\n", null], "names": ["parse"], "mappings": ";;AAAA;AACA;AACA;;;;AAIA,EAAE,SAAS,QAAQ,CAAC,OAAO,EAAE;AAC7B,GAAG,OAAO,OAAO,KAAK,IAAI,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE;AAC3E;;AAEA,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE;AACxD,GAAG,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,UAAU;;AAElC,IAAI,IAAI,MAAM,EAAE;AAChB,KAAK,IAAI,CAAC,MAAM,GAAG;AACnB;;AAEA,IAAI,IAAI,GAAG,EAAE;AACb,KAAK,IAAI,CAAC,GAAG,GAAG;AAChB;;AAEA,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;AAC1B,KAAK,IAAI,CAAC,OAAO,GAAG;AACpB;;AAEA,IAAI,OAAO;AACX;;AAEA,EAAE,SAAS,UAAU,CAAC,GAAG,KAAK,EAAE;AAChC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK;;AAEhC,IAAI,IAAI,MAAM,GAAG;;AAEjB,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE;AAC9B,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE;AACzB,SAAS,MAAM,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,UAAU;AACxD,YAAY,KAAK,CAAC,UAAU,GAAG;AAC/B,YAAY,MAAM,GAAG;AACrB;AACA;;AAEA,GAAG,OAAO;AACV;;AAEA,EAAE,SAAS,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE;AAC9B,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC3C,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC;AACrB,QAAQ,OAAO,IAAI,EAAE;AACrB,SAAS,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;AACvC,YAAY,IAAI,CAAC,IAAI,EAAE;AACvB,aAAa,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;AACxC,aAAa;AACb;AACA,YAAY,IAAI,GAAG;AACnB;AACA;;AAEA,GAAG,OAAO;AACV,KAAK,OAAO;AACZ,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK;AACvD;AACA;;AAEA,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE;AACrC,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC1C,EAAE,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;AAChC,EAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE;AAC3B;;AAEA,SAAS,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC7D,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACtC;AACA,EAAE,IAAI,MAAM,CAAC,cAAc,EAAE;AAC7B,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC;AAC1D;AACA,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ;AAC1B,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK;AACpB,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ;AAC1B,EAAE,IAAI,CAAC,IAAI,GAAG,aAAa;AAC3B,EAAE,OAAO,IAAI;AACb;;AAEA,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC;;AAEpC,SAAS,UAAU,CAAC,GAAG,EAAE,YAAY,EAAE,SAAS,EAAE;AAClD,EAAE,SAAS,GAAG,SAAS,IAAI,GAAG;AAC9B,EAAE,IAAI,GAAG,CAAC,MAAM,GAAG,YAAY,EAAE,EAAE,OAAO,GAAG,CAAC;AAC9C,EAAE,YAAY,IAAI,GAAG,CAAC,MAAM;AAC5B,EAAE,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC;AAC7C,EAAE,OAAO,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC;AAC/C;;AAEA,eAAe,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,EAAE;AACrD,EAAE,IAAI,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO;AACpC,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;AACrB,IAAI,IAAI,GAAG,GAAG,IAAI;AAClB,IAAI,IAAI,CAAC;AACT,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACtD,QAAQ,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;AAClD,QAAQ;AACR;AACA;AACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK;AAC/B,IAAI,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC;AAC/F,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACrC,QAAQ,CAAC;AACT,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,IAAI,GAAG,GAAG,GAAG,QAAQ,CAAC,MAAM;AAChF,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG;AAC/B,MAAM,IAAI,MAAM,GAAG,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC;AACvE,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AAChC,MAAM,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;AAC/D,MAAM,IAAI,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC;AACzC,MAAM,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG;AAC/B,YAAY,MAAM,GAAG;AACrB,YAAY,QAAQ,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG;AAC3C,YAAY,MAAM,GAAG,KAAK,GAAG,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG;AAC7D,YAAY,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC;AACvC,KAAK,MAAM;AACX,MAAM,GAAG,IAAI,QAAQ,GAAG,GAAG;AAC3B;AACA;AACA,EAAE,OAAO,GAAG;AACZ,CAAC;;AAED,eAAe,CAAC,YAAY,GAAG,SAAS,QAAQ,EAAE,KAAK,EAAE;AACzD,EAAE,IAAI,wBAAwB,GAAG;AACjC,IAAI,OAAO,EAAE,SAAS,WAAW,EAAE;AACnC,MAAM,OAAO,IAAI,GAAG,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI;AAC1D,KAAK;;AAEL,IAAI,KAAK,EAAE,SAAS,WAAW,EAAE;AACjC,MAAM,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;AAC9D,QAAQ,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI;AACjC,YAAY,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5D,YAAY,WAAW,CAAC,IAAI,CAAC;AAC7B,OAAO,CAAC;;AAER,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClF,KAAK;;AAEL,IAAI,GAAG,EAAE,WAAW;AACpB,MAAM,OAAO,eAAe;AAC5B,KAAK;;AAEL,IAAI,GAAG,EAAE,WAAW;AACpB,MAAM,OAAO,cAAc;AAC3B,KAAK;;AAEL,IAAI,KAAK,EAAE,SAAS,WAAW,EAAE;AACjC,MAAM,OAAO,WAAW,CAAC,WAAW;AACpC;AACA,GAAG;;AAEH,EAAE,SAAS,GAAG,CAAC,EAAE,EAAE;AACnB,IAAI,OAAO,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE;AACtD;;AAEA,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE;AAC5B,IAAI,OAAO;AACX,OAAO,OAAO,CAAC,KAAK,EAAE,MAAM;AAC5B,OAAO,OAAO,CAAC,IAAI,GAAG,MAAM;AAC5B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,cAAc,WAAW,SAAS,EAAE,EAAE,EAAE,OAAO,MAAM,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;AACjF,OAAO,OAAO,CAAC,uBAAuB,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,KAAK,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAClF;;AAEA,EAAE,SAAS,WAAW,CAAC,CAAC,EAAE;AAC1B,IAAI,OAAO;AACX,OAAO,OAAO,CAAC,KAAK,EAAE,MAAM;AAC5B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,IAAI,GAAG,KAAK;AAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK;AAC3B,OAAO,OAAO,CAAC,cAAc,WAAW,SAAS,EAAE,EAAE,EAAE,OAAO,MAAM,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;AACjF,OAAO,OAAO,CAAC,uBAAuB,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,KAAK,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAClF;;AAEA,EAAE,SAAS,mBAAmB,CAAC,WAAW,EAAE;AAC5C,IAAI,OAAO,wBAAwB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC;AAClE;;AAEA,EAAE,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AACtC,IAAI,IAAI,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,mBAAmB,CAAC;AACxD,IAAI,IAAI,CAAC,EAAE,CAAC;;AAEZ,IAAI,YAAY,CAAC,IAAI,EAAE;;AAEvB,IAAI,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvD,QAAQ,IAAI,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE;AACrD,UAAU,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AAC3C,UAAU,CAAC,EAAE;AACb;AACA;AACA,MAAM,YAAY,CAAC,MAAM,GAAG,CAAC;AAC7B;;AAEA,IAAI,QAAQ,YAAY,CAAC,MAAM;AAC/B,MAAM,KAAK,CAAC;AACZ,QAAQ,OAAO,YAAY,CAAC,CAAC,CAAC;;AAE9B,MAAM,KAAK,CAAC;AACZ,QAAQ,OAAO,YAAY,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC;;AAEzD,MAAM;AACN,QAAQ,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI;AAClD,YAAY;AACZ,YAAY,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AACjD;AACA;;AAEA,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;AAChC,IAAI,OAAO,KAAK,GAAG,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,cAAc;AACtE;;AAEA,EAAE,OAAO,WAAW,GAAG,gBAAgB,CAAC,QAAQ,CAAC,GAAG,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,SAAS;AAC9F,CAAC;;AAED,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;AACnC,EAAE,OAAO,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,GAAG,EAAE;;AAEhD,EAAE,IAAI,UAAU,GAAG,EAAE;AACrB,EAAE,IAAI,UAAU,GAAG,OAAO,CAAC,aAAa;;AAExC,EAAE,IAAI,sBAAsB,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE;AACpD,EAAE,IAAI,qBAAqB,GAAG,YAAY;;AAE1C,EAAE,IAAI,MAAM,GAAG,GAAG;AAClB,EAAE,IAAI,MAAM,GAAG,IAAI;AACnB,EAAE,IAAI,MAAM,GAAG,GAAG;AAClB,EAAE,IAAI,MAAM,GAAG,GAAG;AAClB,EAAE,IAAI,MAAM,GAAG,OAAO;AACtB,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,IAAI,MAAM,GAAG,OAAO;AACtB,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,IAAI,MAAM,GAAG,GAAG;AAClB,EAAE,IAAI,MAAM,GAAG,GAAG;AAClB,EAAE,IAAI,OAAO,GAAG,GAAG;AACnB,EAAE,IAAI,OAAO,GAAG,GAAG;AACnB,EAAE,IAAI,OAAO,GAAG,GAAG;AACnB,EAAE,IAAI,OAAO,GAAG,GAAG;AACnB,EAAE,IAAI,OAAO,GAAG,KAAK;AACrB,EAAE,IAAI,OAAO,GAAG,KAAK;AACrB,EAAE,IAAI,OAAO,GAAG,SAAS;AACzB,EAAE,IAAI,OAAO,GAAG,GAAG;;AAEnB,EAAE,IAAI,MAAM,GAAG,WAAW;AAC1B,EAAE,IAAI,MAAM,GAAG,OAAO;AACtB,EAAE,IAAI,MAAM,GAAG,QAAQ;AACvB,EAAE,IAAI,MAAM,GAAG,MAAM;AACrB,EAAE,IAAI,MAAM,GAAG,iBAAiB;AAChC,EAAE,IAAI,MAAM,GAAG,OAAO;AACtB,EAAE,IAAI,MAAM,GAAG,OAAO;AACtB,EAAE,IAAI,MAAM,GAAG,OAAO;AACtB,EAAE,IAAI,MAAM,GAAG,UAAU;AACzB,EAAE,IAAI,MAAM,GAAG,YAAY;;AAE3B,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,UAAU,CAAC;AAC/C,EAAE,IAAI,MAAM,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AACjD,EAAE,IAAI,MAAM,GAAG,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,MAAM,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AACjD,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,UAAU,CAAC;AAC/C,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC3E,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,WAAW,CAAC;AAChD,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACxD,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,aAAa,CAAC;AAClD,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC/D,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AACzD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,6BAA6B,CAAC;AACnE,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC;AACtD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC;AACpD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC;AACtD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC;AACpD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAClG,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC9D,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,mBAAmB,CAAC;AACzD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAC9D,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAC3C,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,eAAe,CAAC;AACrD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AACxD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,sBAAsB,CAAC;AAC5D,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;AAC/D,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,WAAW,CAAC;AACjD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,yBAAyB,CAAC;AAC/D,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC;AACpD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC;AACpD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC;AACxD,EAAE,IAAI,OAAO,GAAG,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,YAAY,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,oBAAoB,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;;AAE3E,EAAE,IAAI,MAAM,GAAG,SAAS,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;AACpE,EAAE,IAAI,MAAM,GAAG,SAAS,QAAQ,EAAE,EAAE,OAAO,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;AACzE,EAAE,IAAI,MAAM,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;AACzE,EAAE,IAAI,MAAM,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE;AAChE,EAAE,IAAI,MAAM,GAAG,SAAS,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE;AACjG,EAAE,IAAI,MAAM,GAAG,SAAS,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE;AACrH,EAAE,IAAI,MAAM,GAAG,SAAS,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE;AAC3C,EAAE,IAAI,MAAM,GAAG,SAAS,OAAO,EAAE,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;AAC5E,EAAE,IAAI,MAAM,GAAG,SAAS,OAAO,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,EAAE;AAC1D,EAAE,IAAI,MAAM,GAAG,SAAS,IAAI,EAAE,EAAE,OAAO,IAAI,EAAE;AAC7C,EAAE,IAAI,OAAO,GAAG,SAAS,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;AACxE,EAAE,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,GAAG,CAAC;AAE3C,EAAE,IAAI,mBAAmB,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AACpD,EAAE,IAAI,cAAc,GAAG,WAAW;AAClC,EAAE,IAAI,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,IAAI,EAAE;AAC7D,EAAE,IAAI,eAAe,GAAG,OAAO,CAAC,eAAe,GAAG,CAAC;;AAEnD,EAAE,IAAI,UAAU;;AAEhB,EAAE,IAAI,OAAO,CAAC,SAAS,EAAE;AACzB,IAAI,IAAI,EAAE,OAAO,CAAC,SAAS,IAAI,sBAAsB,CAAC,EAAE;AACxD,MAAM,MAAM,IAAI,KAAK,CAAC,kCAAkC,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;AACrF;;AAEA,IAAI,qBAAqB,GAAG,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC;AACrE;;AA0CA,EAAE,SAAS,sBAAsB,CAAC,IAAI,EAAE,UAAU,EAAE;AACpD,IAAI,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE;AAClE;;AAEA,EAAE,SAAS,oBAAoB,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC7D,IAAI,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE;AACtF;;AAMA,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;AAC1B;;AAEA,EAAE,SAAS,oBAAoB,CAAC,WAAW,EAAE;AAC7C,IAAI,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE;AACtD;;AAEA,EAAE,SAAS,qBAAqB,CAAC,GAAG,EAAE;AACtC,IAAI,IAAI,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC;AAC1C,IAAI,IAAI,CAAC;;AAET,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,OAAO;AACpB,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAAE;AAC7C,QAAQ,CAAC,GAAG,mBAAmB,CAAC,MAAM,GAAG,CAAC;AAC1C,OAAO,MAAM;AACb,QAAQ,CAAC,GAAG,GAAG;AACf,QAAQ,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC,EAAE;AAC1C;;AAEA,MAAM,OAAO,GAAG,mBAAmB,CAAC,CAAC,CAAC;AACtC,MAAM,OAAO,GAAG;AAChB,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,MAAM,EAAE,OAAO,CAAC;AACxB,OAAO;;AAEP,MAAM,OAAO,CAAC,GAAG,GAAG,EAAE;AACtB,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;AACxC,UAAU,OAAO,CAAC,IAAI,EAAE;AACxB,UAAU,OAAO,CAAC,MAAM,GAAG,CAAC;AAC5B,SAAS,MAAM;AACf,UAAU,OAAO,CAAC,MAAM,EAAE;AAC1B;;AAEA,QAAQ,CAAC,EAAE;AACX;;AAEA,MAAM,mBAAmB,CAAC,GAAG,CAAC,GAAG,OAAO;;AAExC,MAAM,OAAO,OAAO;AACpB;AACA;;AAEA,EAAE,SAAS,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE;AACzD,IAAI,IAAI,eAAe,GAAG,qBAAqB,CAAC,QAAQ,CAAC;AACzD,IAAI,IAAI,aAAa,GAAG,qBAAqB,CAAC,MAAM,CAAC;;AAErD,IAAI,IAAI,GAAG,GAAG;AACd,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,IAAI,EAAE,eAAe,CAAC,IAAI;AAClC,QAAQ,MAAM,EAAE,eAAe,CAAC;AAChC,OAAO;AACP,MAAM,GAAG,EAAE;AACX,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,IAAI,EAAE,aAAa,CAAC,IAAI;AAChC,QAAQ,MAAM,EAAE,aAAa,CAAC;AAC9B;AACA,KAAK;AAKL,IAAI,OAAO,GAAG;AACd;;AAEA,EAAE,SAAS,QAAQ,CAAC,QAAQ,EAAE;AAC9B,IAAI,IAAI,WAAW,GAAG,cAAc,EAAE,EAAE,OAAO;;AAE/C,IAAI,IAAI,WAAW,GAAG,cAAc,EAAE;AACtC,MAAM,cAAc,GAAG,WAAW;AAClC,MAAM,mBAAmB,GAAG,EAAE;AAC9B;;AAEA,IAAI,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC;AACtC;;AAMA,EAAE,SAAS,wBAAwB,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC/D,IAAI,OAAO,IAAI,eAAe;AAC9B,MAAM,eAAe,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC;AACnD,MAAM,QAAQ;AACd,MAAM,KAAK;AACX,MAAM;AACN,KAAK;AACL;;AAEA,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;;AAElB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,uBAAuB,EAAE;AAClC,IAAI,EAAE,GAAG,wBAAwB,EAAE;AAEnC,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;;AAEvB,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,uBAAuB,GAAG;AACrC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;;AAElB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,EAAE;AACX,IAAI,EAAE,GAAG,gBAAgB,EAAE;AAC3B,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;AAC9B,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACjB,MAAM,EAAE,GAAG,gBAAgB,EAAE;AAC7B;AACA,IAAI,EAAE,GAAG,UAAU,EAAE;AAErB,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;;AAEnB,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAO,IAAC,EAAE,CAAC,CAAK,EAAE,CAAC,CAAK,EAAE,CAAC,CAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAK;;AAEhD,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAS,UAAU,EAAE;AACrB,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AAC9C,MAAM,EAAE,GAAG,MAAM;AACjB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAW,UAAU,EAAE;AACvB,MAAM,EAAE,GAAG,gBAAgB,EAAE;AAC7B,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAa,UAAU,EAAE;AACzB,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AAClD,UAAU,EAAE,GAAG,MAAM;AACrB,UAAU,WAAW,EAAE;AACvB,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACxD;AACA,QAAQ,IAAI,EAAE,KAAK,UAAU,EAAE;AAC/B,UAAU,EAAE,GAAG,iBAAiB,EAAE;AAClC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AACpD,YAAY,EAAE,GAAG,MAAM;AACvB,YAAY,WAAW,EAAE;AACzB,WAAW,MAAM;AACjB,YAAY,EAAE,GAAG,UAAU;AAC3B,YAAY,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1D;AACA,UAAU,IAAI,EAAE,KAAK,UAAU,EAAE;AACjC,YAAiB,UAAU,EAAE;AAC7B,YAAY,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AACtD,cAAc,GAAG,GAAG,MAAM;AAC1B,cAAc,WAAW,EAAE;AAC3B,aAAa,MAAM;AACnB,cAAc,GAAG,GAAG,UAAU;AAC9B,cAAc,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC5D;AACA,YAAY,IAAI,GAAG,KAAK,UAAU,EAAE;AAEpC,cAAc,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;AACjC,aAAa,MAAM;AACnB,cAAc,WAAW,GAAG,EAAE;AAC9B,cAAc,EAAE,GAAG,UAAU;AAC7B;AACA,WAAW,MAAM;AACjB,YAAY,WAAW,GAAG,EAAE;AAC5B,YAAY,EAAE,GAAG,UAAU;AAC3B;AACA,SAAS,MAAM;AACf,UAAU,WAAW,GAAG,EAAE;AAC1B,UAAU,EAAE,GAAG,UAAU;AACzB;AACA,OAAO,MAAM;AACb,QAAQ,WAAW,GAAG,EAAE;AACxB,QAAQ,EAAE,GAAG,UAAU;AACvB;AACA,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAE3B,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;;AAElB,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,EAAE;AACX,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAClC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACzB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,OAAO,EAAE,KAAK,UAAU,EAAE;AAChC,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACnB,QAAQ,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACtC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC7B,UAAU,WAAW,EAAE;AACvB,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACxD;AACA;AACA,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,WAAW,CAAC;AAC3C,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,EAAE;AACb;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;;AAElB,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,EAAE;AACX,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAClC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACzB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpD;AACA,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;AAC9B,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACjB,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACpC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtD;AACA;AACA,IAAI,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,WAAW,CAAC;AACzC,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,UAAU;AACnB,IAAI,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;;AAElD,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,wBAAwB,GAAG;AACtC,IAAO,IAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAK,EAAE;;AAEtB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,aAAa,EAAE;AACxB,IAAS,UAAU,EAAE;AACrB,IAAI,EAAE,GAAG,8BAA8B,EAAE;AACzC,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,IAAI;AACf;AACA,IAAS,UAAU,EAAE;AAErB,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;;AAEvB,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,aAAa,GAAG;AAC3B,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;;AAEtB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,gBAAgB,EAAE;AAC3B,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,IAAI;AACf;AACA,IAAI,EAAE,GAAG,EAAE;AACX,IAAI,EAAE,GAAG,aAAa,EAAE;AACxB,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;AAC9B,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACjB,MAAM,EAAE,GAAG,aAAa,EAAE;AAC1B;AAEA,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;;AAEvB,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,aAAa,GAAG;AAC3B,IAAO,IAAC,EAAE,CAAC,CAAa,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;;AAEhD,IAAI,EAAE,GAAG,WAAW;AACpB,IAAS,UAAU,EAAE;AACrB,IAAS,mBAAmB,EAAE;AAI9B,IAAS,UAAU,EAAE;AACrB,IAAI,EAAE,GAAG,YAAY,EAAE;AACvB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,yBAAyB,EAAE;AACtC,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAQ,EAAE,GAAG,IAAI;AACjB;AACA,MAAM,EAAE,GAAG,EAAE;AACb,MAAM,EAAE,GAAG,YAAY,EAAE;AACzB,MAAM,OAAO,EAAE,KAAK,UAAU,EAAE;AAChC,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACnB,QAAQ,EAAE,GAAG,YAAY,EAAE;AAC3B;AACA,MAAM,EAAE,GAAG,UAAU,EAAE;AACvB,MAAM,EAAE,GAAG,gBAAgB,EAAE;AAC7B,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAQ,EAAE,GAAG,IAAI;AACjB;AACA,MAAM,EAAE,GAAG,EAAE;AACb,MAAM,GAAG,GAAG,kBAAkB,EAAE;AAChC,MAAM,OAAO,GAAG,KAAK,UAAU,EAAE;AACjC,QAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;AACpB,QAAQ,GAAG,GAAG,kBAAkB,EAAE;AAClC;AAEA,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,mBAAmB,GAAG;AACjC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;;AAE9B,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,EAAE;AACX,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAClC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACzB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpD;AACA,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;AAC9B,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACjB,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACpC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtD;AACA;AACA,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AAC9C,MAAM,EAAE,GAAG,MAAM;AACjB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,UAAU,EAAE;AACvB,MAAM,EAAE,GAAG,EAAE;AACb,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACpC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD;AACA,MAAM,OAAO,EAAE,KAAK,UAAU,EAAE;AAChC,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACnB,QAAQ,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACtC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC7B,UAAU,WAAW,EAAE;AACvB,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACzD;AACA;AACA,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,MAAM,EAAE,GAAG,EAAE;AACb,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;;AAE9B,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE;AACjD,MAAM,EAAE,GAAG,MAAM;AACjB,MAAM,WAAW,IAAI,CAAC;AACtB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE;AACnD,QAAQ,EAAE,GAAG,MAAM;AACnB,QAAQ,WAAW,IAAI,CAAC;AACxB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD;AACA,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE;AACrD,UAAU,EAAE,GAAG,MAAM;AACrB,UAAU,WAAW,IAAI,CAAC;AAC1B,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACzD;AACA,QAAQ,IAAI,EAAE,KAAK,UAAU,EAAE;AAC/B,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE;AACvD,YAAY,EAAE,GAAG,MAAM;AACvB,YAAY,WAAW,IAAI,CAAC;AAC5B,WAAW,MAAM;AACjB,YAAY,EAAE,GAAG,UAAU;AAC3B,YAAY,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC3D;AACA,UAAU,IAAI,EAAE,KAAK,UAAU,EAAE;AACjC,YAAY,EAAE,GAAG,WAAW;AAC5B,YAAY,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAC1C,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACjC,cAAc,WAAW,EAAE;AAC3B,aAAa,MAAM;AACnB,cAAc,EAAE,GAAG,UAAU;AAC7B,cAAc,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC5D;AACA,YAAY,IAAI,EAAE,KAAK,UAAU,EAAE;AACnC,cAAc,EAAE,GAAG,EAAE;AACrB,cAAc,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAC5C,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACnC,gBAAgB,WAAW,EAAE;AAC7B,eAAe,MAAM;AACrB,gBAAgB,EAAE,GAAG,UAAU;AAC/B,gBAAgB,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC/D;AACA,cAAc,IAAI,EAAE,KAAK,UAAU,EAAE;AACrC,gBAAgB,OAAO,EAAE,KAAK,UAAU,EAAE;AAC1C,kBAAkB,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AAC7B,kBAAkB,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAChD,kBAAkB,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACvC,oBAAoB,WAAW,EAAE;AACjC,mBAAmB,MAAM;AACzB,oBAAoB,EAAE,GAAG,UAAU;AACnC,oBAAoB,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACnE;AACA;AACA,eAAe,MAAM;AACrB,gBAAgB,EAAE,GAAG,UAAU;AAC/B;AACA,cAAc,IAAI,EAAE,KAAK,UAAU,EAAE;AACrC,gBAAgB,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AAC7B,gBAAgB,EAAE,GAAG,EAAE;AACvB,eAAe,MAAM;AACrB,gBAAgB,WAAW,GAAG,EAAE;AAChC,gBAAgB,EAAE,GAAG,UAAU;AAC/B;AACA,aAAa,MAAM;AACnB,cAAc,WAAW,GAAG,EAAE;AAC9B,cAAc,EAAE,GAAG,UAAU;AAC7B;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACpC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD;AACA,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAQ,EAAE,GAAG,IAAI;AACjB;AACA,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACnB,MAAM,EAAE,GAAG,EAAE;AACb,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,WAAW,CAAC;AAC3C,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,EAAE;AACb;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,yBAAyB,GAAG;AACvC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;;AAElB,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,EAAE,GAAG,EAAE;AACX,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAClC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACzB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;AAC9B,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACjB,MAAM,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,EAAE;AAC1B,QAAQ,EAAE,GAAG,UAAU;AACvB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACtC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC7B,UAAU,WAAW,EAAE;AACvB,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACzD;AACA;AACA;AACA,IAAI,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,EAAE;AACb;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAO,IAAC,EAAE,CAAC,CAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;;AAE5B,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAS,UAAU,EAAE;AACrB,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AAC9C,MAAM,EAAE,GAAG,MAAM;AACjB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,WAAW;AACtB,MAAM,EAAE,GAAG,EAAE;AACb,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACpC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtD;AACA,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAQ,OAAO,EAAE,KAAK,UAAU,EAAE;AAClC,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACrB,UAAU,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACxC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC/B,YAAY,WAAW,EAAE;AACzB,WAAW,MAAM;AACjB,YAAY,EAAE,GAAG,UAAU;AAC3B,YAAY,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1D;AACA;AACA,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB;AACA,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAQ,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,WAAW,CAAC;AAC7C,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,EAAE;AACf;AACA,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAE7B,QAAQ,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACvB,OAAO,MAAM;AACb,QAAQ,WAAW,GAAG,EAAE;AACxB,QAAQ,EAAE,GAAG,UAAU;AACvB;AACA,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAE3B,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,IAAI,EAAE;;AAEV,IAAI,EAAE,GAAG,qBAAqB,EAAE;AAChC,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,0BAA0B,EAAE;AACvC;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,qBAAqB,GAAG;AACnC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;;AAE1B,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;AAC/C,MAAM,EAAE,GAAG,MAAM;AACjB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,WAAW;AACtB,MAAM,EAAE,GAAG,EAAE;AACb,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACpC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD;AACA,MAAM,OAAO,EAAE,KAAK,UAAU,EAAE;AAChC,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACnB,QAAQ,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACtC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC7B,UAAU,WAAW,EAAE;AACvB,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACzD;AACA;AACA,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,WAAW,CAAC;AAC3C,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;AACjD,QAAQ,EAAE,GAAG,OAAO;AACpB,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD;AACA,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAE7B,QAAQ,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACvB,OAAO,MAAM;AACb,QAAQ,WAAW,GAAG,EAAE;AACxB,QAAQ,EAAE,GAAG,UAAU;AACvB;AACA,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,0BAA0B,GAAG;AACxC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;;AAE1B,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AAC9C,MAAM,EAAE,GAAG,OAAO;AAClB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,WAAW;AACtB,MAAM,EAAE,GAAG,EAAE;AACb,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACpC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD;AACA,MAAM,OAAO,EAAE,KAAK,UAAU,EAAE;AAChC,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACnB,QAAQ,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACtC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC7B,UAAU,WAAW,EAAE;AACvB,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACzD;AACA;AACA,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,WAAW,CAAC;AAE3C,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAO,IAAC,EAAE,CAAC,CAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAK;;AAE5B,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAS,UAAU,EAAE;AACrB,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AAC9C,MAAM,EAAE,GAAG,OAAO;AAClB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,aAAa,EAAE;AAC1B,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAa,UAAU,EAAE;AACzB,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AAClD,UAAU,EAAE,GAAG,OAAO;AACtB,UAAU,WAAW,EAAE;AACvB,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACzD;AACA,QAAQ,IAAI,EAAE,KAAK,UAAU,EAAE;AAE/B,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACzB,SAAS,MAAM;AACf,UAAU,WAAW,GAAG,EAAE;AAC1B,UAAU,EAAE,GAAG,UAAU;AACzB;AACA,OAAO,MAAM;AACb,QAAQ,WAAW,GAAG,EAAE;AACxB,QAAQ,EAAE,GAAG,UAAU;AACvB;AACA,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAE3B,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,8BAA8B,GAAG;AAC5C,IAAO,IAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAK;;AAEpB,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,WAAW;AACpB,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;AAClD,MAAM,EAAE,GAAG,OAAO;AAClB,MAAM,WAAW,IAAI,CAAC;AACtB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;AACpD,QAAQ,EAAE,GAAG,OAAO;AACpB,QAAQ,WAAW,IAAI,CAAC;AACxB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD;AACA,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;AACtD,UAAU,EAAE,GAAG,OAAO;AACtB,UAAU,WAAW,IAAI,CAAC;AAC1B,SAAS,MAAM;AACf,UAAU,EAAE,GAAG,UAAU;AACzB,UAAU,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACzD;AACA,QAAQ,IAAI,EAAE,KAAK,UAAU,EAAE;AAC/B,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;AACpD,YAAY,EAAE,GAAG,OAAO;AACxB,YAAY,WAAW,EAAE;AACzB,WAAW,MAAM;AACjB,YAAY,EAAE,GAAG,UAAU;AAC3B,YAAY,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAW,UAAU,EAAE;AACvB,MAAM,EAAE,GAAG,gBAAgB,EAAE;AAC7B,MAAM,IAAI,EAAE,KAAK,UAAU,EAAE;AAC7B,QAAQ,EAAE,GAAG,IAAI;AACjB;AAEA,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;AAC1B,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,EAAE;AACtB,MAAM,EAAE,GAAG,UAAU;AACrB;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,IAAI,EAAE,KAAK,UAAU,EAAE;AAC3B,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;;AAEA,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAI,EAAE,EAAE,EAAE;;AAEd,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,EAAE;AACX,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAClC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACzB,MAAM,WAAW,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,EAAE,GAAG,UAAU;AACrB,MAAM,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;AAC9B,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACjB,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACpC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,MAAM;AACb,QAAQ,EAAE,GAAG,UAAU;AACvB,QAAQ,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvD;AACA;AACA,IAAI,eAAe,EAAE;AACrB,IAAI,EAAE,GAAG,UAAU;AACnB,IAAI,IAAI,eAAe,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;;AAEnD,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,UAAU,GAAG,qBAAqB,EAAE;;AAEtC,EAAE,IAAI,OAAO,CAAC,WAAW,EAAE;AAC3B,IAAI,2BAA2B;AAC/B,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,UAAU;AAChB,MAAM,mBAAmB;AACzB,MAAM;AACN,KAAK;AACL;AACA,EAAE,IAAI,UAAU,KAAK,UAAU,IAAI,WAAW,KAAK,KAAK,CAAC,MAAM,EAAE;AACjE,IAAI,OAAO,UAAU;AACrB,GAAG,MAAM;AACT,IAAI,IAAI,UAAU,KAAK,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,MAAM,EAAE;AACjE,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;AACpC;;AAEA,IAAI,MAAM,wBAAwB;AAClC,MAAM,mBAAmB;AACzB,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,IAAI;AACzE,MAAM,cAAc,GAAG,KAAK,CAAC;AAC7B,UAAU,mBAAmB,CAAC,cAAc,EAAE,cAAc,GAAG,CAAC;AAChE,UAAU,mBAAmB,CAAC,cAAc,EAAE,cAAc;AAC5D,KAAK;AACL;AACA;;ACtxCA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AAIH,MAAM,MAAM,GAAG,mBAAmB;AAElC,SAAS,IAAI,CAAC,CAAS,EAAE,CAAS,EAAA;AAChC,IAAA,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,mBAAmB;AAC5D;AAEA,SAAS,WAAW,CAAC,CAAS,EAAE,CAAS,EAAA;AACvC,IAAA,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM;AACzB;AAEA;AACM,SAAU,YAAY,CAAC,KAAa,EAAA;IACxC,OAAO,YAAA;QACL,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;AAC/B,QAAA,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,MAAM,CAAC;AAExC,QAAA,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAE7D,EAAE,IAAI,EAAE;AACR,QAAA,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,IAAI,MAAM;AAChD,QAAA,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC;QAElB,KAAK,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE;AAExB,QAAA,OAAO,MAAM;AACf,KAAC;AACH;AAEA,MAAM,IAAI,GAAG,YAAY,CAAC,mCAAmC,CAAC;AAE9D,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,MAC3C,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC,CAAC,CAC3E;AAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC;AAEvD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC;AAE9D,MAAM,QAAQ,GAAG,IAAI,EAAE;AAEhB,MAAM,KAAK,GAAG;AACd,MAAM,KAAK,GAAG;AAEd,MAAM,IAAI,GAAG;AACb,MAAM,MAAM,GAAG;AACf,MAAM,MAAM,GAAG;AACf,MAAM,IAAI,GAAG;AACb,MAAM,KAAK,GAAG;AACd,MAAM,IAAI,GAAG;AAgBb,MAAM,gBAAgB,GAC3B;MA2BW,IAAI,CAAA;AACf,IAAA,KAAK;AACL,IAAA,IAAI;AACJ,IAAA,EAAE;AACF,IAAA,KAAK;AACL,IAAA,QAAQ;AACR,IAAA,SAAS;AAET;;;;;AAKG;AACH,IAAA,KAAK;AAEL,IAAA,GAAG;AACH,IAAA,GAAG;AACH,IAAA,MAAM;AACN,IAAA,KAAK;IAEL,WAAY,CAAA,KAAY,EAAE,QAAsB,EAAA;AAC9C,QAAA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,QAAQ;AAEvE,QAAA,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC;AACrC,QAAA,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC;AAEjC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,IAAI,GAAG,aAAa;AACzB,QAAA,IAAI,CAAC,EAAE,GAAG,WAAW;AAErB;;;;AAIG;QAEH,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,GAAG,GAAG,aAAa,GAAG,WAAW;AACtC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE;;AAGzB,QAAA,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC;AAC5B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE;AACxB,QAAA,KAAK,CAAC,WAAW,CAAC,EAAE;;AAGpB,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;AACf,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;AACvB,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE;AACtB,gBAAA,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC;AAC1B;AACF;AAED,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACzB;AAED,QAAA,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS;AAC1B,YAAA,IAAI,CAAC,GAAG,IAAI,SAAS;AACtB;;IAGH,SAAS,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE;;IAGlD,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE;;IAGpD,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE;;IAGrD,gBAAgB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,GAAG,EAAE;;IAGvD,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,GAAG,EAAE;;IAGvD,SAAS,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE;;AAEpD;AAED,MAAM,KAAK,GAAG,EAAE;AAEhB,MAAM,KAAK,GAA2B;AACpC,IAAA,MAAM,EAAE,GAAG;AACX,IAAA,OAAO,EAAE,GAAG;AACZ,IAAA,QAAQ,EAAE,GAAG;AACb,IAAA,UAAU,EAAE,GAAG;AACf,IAAA,SAAS,EAAE,GAAG;AACd,IAAA,YAAY,EAAE,GAAG;AACjB,IAAA,YAAY,EAAE,GAAG;AACjB,IAAA,SAAS,EAAE,GAAG;CACf;AAED;AACa,MAAA,OAAO,GAAa;AAC/B,IAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9C,IAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9C,IAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9C,IAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9C,IAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9C,IAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9C,IAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9C,IAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;;AAG5C,MAAM,IAAI,GAA2B;AACnC,IAAA,MAAM,EAAE,CAAC;AACT,IAAA,OAAO,EAAE,CAAC;AACV,IAAA,QAAQ,EAAE,CAAC;AACX,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,SAAS,EAAE,EAAE;AACb,IAAA,YAAY,EAAE,EAAE;AAChB,IAAA,YAAY,EAAE,EAAE;AAChB,IAAA,SAAS,EAAE,GAAG;CACf;AAED;AAEA;AACa,MAAA,gBAAgB,GAA2B;AACtD,IAAA,KAAK,EAAE,GAAG;AACV,IAAA,IAAI,EAAE,GAAG;AACT,IAAA,IAAI,EAAE,YAAY;AAClB,IAAA,KAAK,EAAE,GAAG;AACV,IAAA,KAAK,EAAE,GAAG;AACV,IAAA,KAAK,EAAE,GAAG;AACV,IAAA,MAAM,EAAE,GAAG;;AAGb;;;AAGG;AACH,MAAM,gBAAgB,GAAkC;AACtD,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,GAAG,EAAE,IAAI;AACT,IAAA,GAAG,EAAE,IAAI;AACT,IAAA,IAAI,EAAE,IAAI;AACV,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,GAAG,EAAE,IAAI;AACT,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,IAAI,EAAE,IAAI;AACV,IAAA,QAAQ,EAAE,IAAI;CACf;AAED,MAAM,eAAe,GAAG;AACtB,IAAA,GAAG,gBAAgB;AACnB,IAAA,GAAG,gBAAgB;CACpB;AACD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCG;AAEH;AACA;AACA,MAAM,IAAI,GAA2B;AACnC,IAAA,EAAE,EAAI,CAAC,EAAE,EAAE,EAAI,CAAC,EAAE,EAAE,EAAI,CAAC,EAAE,EAAE,EAAI,CAAC,EAAE,EAAE,EAAI,CAAC,EAAE,EAAE,EAAI,CAAC,EAAE,EAAE,EAAI,CAAC,EAAE,EAAE,EAAI,CAAC;AACtE,IAAA,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE;AACtE,IAAA,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE;AACtE,IAAA,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE;AACtE,IAAA,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE;AACtE,IAAA,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE;AACtE,IAAA,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;AACtE,IAAA,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;CACpE;AAED,MAAM,YAAY,GAAG;IACnB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,IAAA,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;CACxB;AAED,MAAM,aAAa,GAAG;IACpB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACvC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;IACrB,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IACnB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACrC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CACtC;AAED;AACA,MAAM,OAAO,GAAG;AACd,IAAA,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC;AAChD,IAAA,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACjD,IAAA,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAG,CAAC,EAAE,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChD,IAAA,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IACjD,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;CAC7C;AAED;AACA,MAAM,IAAI,GAAG;AACV,IAAA,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAG,EAAE,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC;AAC5D,IAAA,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAE,CAAC;AAC9D,IAAA,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC,GAAG,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAC;CACzD;AAED,MAAM,WAAW,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;AAExE,MAAM,OAAO,GAAG,cAAc;AAE9B,MAAM,UAAU,GAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;AAE/D,MAAM,MAAM,GAAG,CAAC;AAChB,MAAM,MAAM,GAAG,CAAC;AAChB;;;;;AAKG;AACH,MAAM,MAAM,GAAG,CAAC;AAChB,MAAM,MAAM,GAAG,CAAC;AAEhB,MAAM,KAAK,GAAG;AACZ,IAAA,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY;AACzB,IAAA,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY;CAC3B;AAED,MAAM,KAAK,GAAG;AACZ,IAAA,CAAC,EAAE;QACD,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;QAC5C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;AAC7C,KAAA;AACD,IAAA,CAAC,EAAE;QACD,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;QAC5C,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;AAC7C,KAAA;CACF;AAED,MAAM,WAAW,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE;AAI5C,MAAM,YAAY,GAAG,IAAI;AAEzB;AACA,SAAS,IAAI,CAAC,MAAc,EAAA;IAC1B,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA;AACA,SAAS,IAAI,CAAC,MAAc,EAAA;IAC1B,OAAO,MAAM,GAAG,GAAG;AACrB;AAEA,SAAS,OAAO,CAAC,CAAS,EAAA;IACxB,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE;AACvC;AAEA;AACA,SAAS,SAAS,CAAC,MAAc,EAAA;AAC/B,IAAA,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACtB,IAAA,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACtB,QAAQ,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QACpC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAClC;AAEA,SAAS,SAAS,CAAC,KAAY,EAAA;IAC7B,OAAO,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;AACxC;AAEM,SAAU,WAAW,CAAC,GAAW,EAAA;;IAErC,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AAC/B,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO;AACL,YAAA,EAAE,EAAE,KAAK;AACT,YAAA,KAAK,EAAE,sDAAsD;SAC9D;AACF;;IAGD,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1C,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,UAAU,IAAI,CAAC,EAAE;QACxC,OAAO;AACL,YAAA,EAAE,EAAE,KAAK;AACT,YAAA,KAAK,EAAE,qDAAqD;SAC7D;AACF;;IAGD,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE;QACrC,OAAO;AACL,YAAA,EAAE,EAAE,KAAK;AACT,YAAA,KAAK,EACH,sEAAsE;SACzE;AACF;;IAGD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3C,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,2CAA2C,EAAE;AACzE;;IAGD,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9B,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,+CAA+C,EAAE;AAC7E;;IAGD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9B,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,sCAAsC,EAAE;AACpE;;IAGD,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;AACjC,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO;AACL,YAAA,EAAE,EAAE,KAAK;AACT,YAAA,KAAK,EAAE,+DAA+D;SACvE;AACF;;AAGD,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;QAEpC,IAAI,SAAS,GAAG,CAAC;QACjB,IAAI,iBAAiB,GAAG,KAAK;AAE7B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACvB,gBAAA,IAAI,iBAAiB,EAAE;oBACrB,OAAO;AACL,wBAAA,EAAE,EAAE,KAAK;AACT,wBAAA,KAAK,EAAE,yDAAyD;qBACjE;AACF;AACD,gBAAA,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBACrC,iBAAiB,GAAG,IAAI;AACzB;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACxC,OAAO;AACL,wBAAA,EAAE,EAAE,KAAK;AACT,wBAAA,KAAK,EAAE,oDAAoD;qBAC5D;AACF;gBACD,SAAS,IAAI,CAAC;gBACd,iBAAiB,GAAG,KAAK;AAC1B;AACF;QACD,IAAI,SAAS,KAAK,CAAC,EAAE;YACnB,OAAO;AACL,gBAAA,EAAE,EAAE,KAAK;AACT,gBAAA,KAAK,EAAE,+DAA+D;aACvE;AACF;AACF;;AAGD,IAAA,IACE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG;AACxC,SAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EACzC;QACA,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,wCAAwC,EAAE;AACtE;;AAGD,IAAA,MAAM,KAAK,GAAG;AACZ,QAAA,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;AAC/B,QAAA,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;KAChC;IAED,KAAK,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,KAAK,EAAE;QACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1B,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAwB,qBAAA,EAAA,KAAK,CAAO,KAAA,CAAA,EAAE;AAClE;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE;YAC7C,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAyB,sBAAA,EAAA,KAAK,CAAQ,MAAA,CAAA,EAAE;AACpE;AACF;;AAGD,IAAA,IACE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,EACxE;QACA,OAAO;AACL,YAAA,EAAE,EAAE,KAAK;AACT,YAAA,KAAK,EAAE,8CAA8C;SACtD;AACF;AAED,IAAA,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE;AACrB;AAEA;AACA,SAAS,gBAAgB,CAAC,IAAkB,EAAE,KAAqB,EAAA;AACjE,IAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;AACtB,IAAA,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;AAClB,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;IAExB,IAAI,WAAW,GAAG,CAAC;IACnB,IAAI,QAAQ,GAAG,CAAC;IAChB,IAAI,QAAQ,GAAG,CAAC;AAEhB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAChD,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC/B,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;AAEjC;;;AAGG;QACH,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,KAAK,OAAO,EAAE;AAChE,YAAA,WAAW,EAAE;YAEb,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE;AAClC,gBAAA,QAAQ,EAAE;AACX;YAED,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE;AAClC,gBAAA,QAAQ,EAAE;AACX;AACF;AACF;IAED,IAAI,WAAW,GAAG,CAAC,EAAE;AACnB,QAAA,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;AAChC;;;AAGG;AACH,YAAA,OAAO,SAAS,CAAC,IAAI,CAAC;AACvB;aAAM,IAAI,QAAQ,GAAG,CAAC,EAAE;AACvB;;;AAGG;YACH,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AACjC;AAAM,aAAA;;YAEL,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AACjC;AACF;AAED,IAAA,OAAO,EAAE;AACX;AAEA,SAAS,OAAO,CACd,KAAqB,EACrB,KAAY,EACZ,IAAY,EACZ,EAAU,EACV,KAAkB,EAClB,QAAoC,GAAA,SAAS,EAC7C,KAAgB,GAAA,IAAI,CAAC,MAAM,EAAA;AAE3B,IAAA,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAElB,IAAA,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE;AACpD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,YAAA,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC;YAC/B,KAAK,CAAC,IAAI,CAAC;gBACT,KAAK;gBACL,IAAI;gBACJ,EAAE;gBACF,KAAK;gBACL,QAAQ;gBACR,SAAS;AACT,gBAAA,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS;AAC9B,aAAA,CAAC;AACH;AACF;AAAM,SAAA;QACL,KAAK,CAAC,IAAI,CAAC;YACT,KAAK;YACL,IAAI;YACJ,EAAE;YACF,KAAK;YACL,QAAQ;YACR,KAAK;AACN,SAAA,CAAC;AACH;AACH;AAEA,SAAS,cAAc,CAAC,GAAW,EAAA;IACjC,IAAI,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7B,IAAA,IAAI,SAAS,IAAI,GAAG,IAAI,SAAS,IAAI,GAAG,EAAE;QACxC,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC;AAC7C,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,SAAS;AACjB;AACD,QAAA,OAAO,IAAI;AACZ;AACD,IAAA,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE;IACnC,IAAI,SAAS,KAAK,GAAG,EAAE;AACrB,QAAA,OAAO,IAAI;AACZ;AACD,IAAA,OAAO,SAAwB;AACjC;AAEA;AACA,SAAS,WAAW,CAAC,IAAY,EAAA;AAC/B,IAAA,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;AACzD;MAEa,KAAK,CAAA;AACR,IAAA,MAAM,GAAG,IAAI,KAAK,CAAQ,GAAG,CAAC;IAC9B,KAAK,GAAU,KAAK;IACpB,OAAO,GAAkC,EAAE;IAC3C,MAAM,GAA0B,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE;IACtD,SAAS,GAAG,EAAE;IACd,UAAU,GAAG,CAAC;IACd,WAAW,GAAG,CAAC;IACf,QAAQ,GAAc,EAAE;IACxB,SAAS,GAA2B,EAAE;IACtC,SAAS,GAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IAEjD,KAAK,GAAG,EAAE;;AAGV,IAAA,cAAc,GAAG,IAAI,GAAG,EAAkB;IAElD,WAAY,CAAA,GAAG,GAAG,gBAAgB,EAAE,EAAE,cAAc,GAAG,KAAK,EAAE,GAAG,EAAE,EAAA;QACjE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,cAAc,EAAE,CAAC;;AAGpC,IAAA,KAAK,CAAC,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,EAAE,EAAA;QACpC,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,CAAQ,GAAG,CAAC;AACnC,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE;AACpC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC/B,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC;AACnB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,eAAe,GAAG,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,eAAe,EAAE;AACtE,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;AAChC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAkB;AAE/C;;;;AAIG;AACH,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI;AAC5B,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI;;AAG5B,IAAA,IAAI,CAAC,GAAW,EAAE,EAAE,cAAc,GAAG,KAAK,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,EAAE,EAAA;QACxE,IAAI,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;;QAG7B,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3C,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACxC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AACvE;AAED,QAAA,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;QAEzB,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC;YACtC,IAAI,CAAC,EAAE,EAAE;AACP,gBAAA,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;AACvB;AACF;AAED,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;QAC1B,IAAI,MAAM,GAAG,CAAC;AAEd,QAAA,IAAI,CAAC,KAAK,CAAC,EAAE,eAAe,EAAE,CAAC;AAE/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YAEhC,IAAI,KAAK,KAAK,GAAG,EAAE;gBACjB,MAAM,IAAI,CAAC;AACZ;AAAM,iBAAA,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,gBAAA,MAAM,IAAI,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;AAC9B;AAAM,iBAAA;AACL,gBAAA,MAAM,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK;AACzC,gBAAA,IAAI,CAAC,IAAI,CACP,EAAE,IAAI,EAAE,KAAK,CAAC,WAAW,EAAiB,EAAE,KAAK,EAAE,EACnD,SAAS,CAAC,MAAM,CAAC,CAClB;AACD,gBAAA,MAAM,EAAE;AACT;AACF;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAU;AAE/B,QAAA,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY;AACtC;AACD,QAAA,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY;AACtC;AACD,QAAA,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY;AACtC;AACD,QAAA,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY;AACtC;QAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAW,CAAC;AACtE,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AAE1C,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;AAChC,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACtB,IAAI,CAAC,iBAAiB,EAAE;;AAG1B,IAAA,GAAG,CAAC,EACF,oBAAoB,GAAG,KAAK,MACU,EAAE,EAAA;QACxC,IAAI,KAAK,GAAG,CAAC;QACb,IAAI,GAAG,GAAG,EAAE;AAEZ,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;AACvC,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBAClB,IAAI,KAAK,GAAG,CAAC,EAAE;oBACb,GAAG,IAAI,KAAK;oBACZ,KAAK,GAAG,CAAC;AACV;AACD,gBAAA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAE7C,gBAAA,GAAG,IAAI,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE;AACnE;AAAM,iBAAA;AACL,gBAAA,KAAK,EAAE;AACR;AAED,YAAA,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;gBAClB,IAAI,KAAK,GAAG,CAAC,EAAE;oBACb,GAAG,IAAI,KAAK;AACb;AAED,gBAAA,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE;oBACjB,GAAG,IAAI,GAAG;AACX;gBAED,KAAK,GAAG,CAAC;gBACT,CAAC,IAAI,CAAC;AACP;AACF;QAED,IAAI,QAAQ,GAAG,EAAE;QACjB,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;YAC7C,QAAQ,IAAI,GAAG;AAChB;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;YAC7C,QAAQ,IAAI,GAAG;AAChB;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;YAC7C,QAAQ,IAAI,GAAG;AAChB;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;YAC7C,QAAQ,IAAI,GAAG;AAChB;;AAGD,QAAA,QAAQ,GAAG,QAAQ,IAAI,GAAG;QAE1B,IAAI,QAAQ,GAAG,GAAG;AAClB;;;AAGG;AACH,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAC5B,YAAA,IAAI,oBAAoB,EAAE;AACxB,gBAAA,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;AACrC;AAAM,iBAAA;gBACL,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;gBACxE,MAAM,OAAO,GAAG,CAAC,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC;AAEtD,gBAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;;oBAE5B,IAAI,MAAM,GAAG,IAAI,EAAE;wBACjB;AACD;AAED,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;;oBAGxB,IACE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,KAAK,KAAK;wBACpC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,KAAK,IAAI,EAClC;;wBAEA,IAAI,CAAC,SAAS,CAAC;4BACb,KAAK;AACL,4BAAA,IAAI,EAAE,MAAM;4BACZ,EAAE,EAAE,IAAI,CAAC,SAAS;AAClB,4BAAA,KAAK,EAAE,IAAI;AACX,4BAAA,QAAQ,EAAE,IAAI;4BACd,KAAK,EAAE,IAAI,CAAC,UAAU;AACvB,yBAAA,CAAC;wBACF,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;wBAC5C,IAAI,CAAC,SAAS,EAAE;;AAGhB,wBAAA,IAAI,OAAO,EAAE;AACX,4BAAA,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;4BACpC;AACD;AACF;AACF;AACF;AACF;QAED,OAAO;YACL,GAAG;AACH,YAAA,IAAI,CAAC,KAAK;YACV,QAAQ;YACR,QAAQ;AACR,YAAA,IAAI,CAAC,UAAU;AACf,YAAA,IAAI,CAAC,WAAW;AACjB,SAAA,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGL,IAAA,SAAS,CAAC,CAAS,EAAA;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AACnB,YAAA,OAAO,EAAE;AACV;AAED,QAAA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAEtC,QAAA,MAAM,UAAU,GAAG;AACjB,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;SACL,CAAC,KAAK,CAAC;AAER,QAAA,MAAM,SAAS,GAAG;AAChB,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;SACL,CAAC,IAAI,CAAC;QAEP,OAAO,UAAU,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;IAGrC,MAAM,GAAA;QACZ,OAAO,IAAI,CAAC,SAAS,KAAK,KAAK,GAAG,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;;IAG5D,YAAY,GAAA;QAClB,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC;AAC/D,QAAA,OAAO,aAAa,CAAC,KAAK,CAAC;;IAGrB,YAAY,GAAA;QAClB,IAAI,IAAI,GAAG,EAAE;AAEb,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;;YAEvC,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,CAAC,IAAI,CAAC;gBACN;AACD;AAED,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AAClB,gBAAA,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC1B;AACF;AAED,QAAA,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,QAAA,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AAE3B,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,EAAE;YACtB,IAAI,IAAI,QAAQ;AACjB;AAED,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;AACK,IAAA,YAAY,CAAC,GAAW,EAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;YAAE;QAE9B,IAAI,GAAG,KAAK,gBAAgB,EAAE;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG;AAC3B,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG;AAC1B;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI;AAC3B;;IAGH,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAG7B,IAAA,GAAG,CAAC,MAAc,EAAA;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;AAGlC,IAAA,SAAS,CAAC,KAAY,EAAA;QACpB,MAAM,OAAO,GAAa,EAAE;AAC5B,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;;YAEvC,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,CAAC,IAAI,CAAC;gBACN;AACD;;YAGD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;gBAC5D;AACD;;YAGD,IACE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK;gBACpC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAClC;gBACA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC3B;AACF;AAED,QAAA,OAAO,OAAO;;AAGhB,IAAA,GAAG,CACD,EAAE,IAAI,EAAE,KAAK,EAAuC,EACpD,MAAc,EAAA;AAEd,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC,EAAE;YACtC,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAC7B,YAAA,OAAO,IAAI;AACZ;AACD,QAAA,OAAO,KAAK;;IAGN,IAAI,CAAC,EAAU,EAAE,KAAY,EAAA;QACnC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK;QACvB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;;AAG1B,IAAA,IAAI,CACV,EAAE,IAAI,EAAE,KAAK,EAAuC,EACpD,MAAc,EAAA;;AAGd,QAAA,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,EAAE,EAAE;AAC9C,YAAA,OAAO,KAAK;AACb;;AAGD,QAAA,IAAI,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE;AACrB,YAAA,OAAO,KAAK;AACb;AAED,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;;QAGvB,IACE,IAAI,IAAI,IAAI;AACZ,YAAA,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAC1D;AACA,YAAA,OAAO,KAAK;AACb;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;;AAG5C,QAAA,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,IAAI,KAAK,IAAI,EAAE;YAC9D,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,KAAK;AAChD;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAmB,EAAE,KAAK,EAAE,KAAc,EAAE,CAAC;QAEnE,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;AACxB;AAED,QAAA,OAAO,IAAI;;AAGL,IAAA,MAAM,CAAC,EAAU,EAAA;QACvB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AAChC,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;;AAGxB,IAAA,MAAM,CAAC,MAAc,EAAA;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzB,QAAA,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK;AACjC;QAED,IAAI,CAAC,qBAAqB,EAAE;QAC5B,IAAI,CAAC,sBAAsB,EAAE;QAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAE7B,QAAA,OAAO,KAAK;;IAGN,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;AAEjC,QAAA,MAAM,gBAAgB,GACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,KAAK;AACvC,QAAA,MAAM,gBAAgB,GACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,KAAK;AAEvC,QAAA,IACE,CAAC,gBAAgB;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,KAAK,EACrC;YACA,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,GAAkB;AACvC;AAED,QAAA,IACE,CAAC,gBAAgB;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,KAAK,EACrC;YACA,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,GAAkB;AACvC;AAED,QAAA,IACE,CAAC,gBAAgB;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,KAAK,EACrC;YACA,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,GAAkB;AACvC;AAED,QAAA,IACE,CAAC,gBAAgB;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,KAAK,EACrC;YACA,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,GAAkB;AACvC;AAED,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;;IAG3B,sBAAsB,GAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC5B;AACD;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC;QACtE,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;QACxE,MAAM,SAAS,GAAG,CAAC,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC;AAExD,QAAA,IACE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,IAAI;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;AACpC,YAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,KAAK,KAAK,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,IAAI,KAAK,IAAI,EACzC;AACA,YAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;YACtB;AACD;AAED,QAAA,MAAM,UAAU,GAAG,CAAC,MAAc,KAChC,EAAE,MAAM,GAAG,IAAI,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,KAAK;YACzC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,KAAK,IAAI;AAEpC,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAC/B,YAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACvB;;AAMK,IAAA,SAAS,CAAC,KAAY,EAAE,MAAc,EAAE,OAAiB,EAAA;QAC/D,MAAM,SAAS,GAAa,EAAE;AAC9B,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;;YAEvC,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,CAAC,IAAI,CAAC;gBACN;AACD;;AAGD,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE;gBAClE;AACD;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5B,YAAA,MAAM,UAAU,GAAG,CAAC,GAAG,MAAM;;YAG7B,IAAI,UAAU,KAAK,CAAC,EAAE;gBACpB;AACD;AAED,YAAA,MAAM,KAAK,GAAG,UAAU,GAAG,GAAG;YAE9B,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AAC5C,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;oBACvB,IACE,CAAC,UAAU,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;yBACvC,UAAU,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAC1C;wBACA,IAAI,CAAC,OAAO,EAAE;AACZ,4BAAA,OAAO,IAAI;AACZ;AAAM,6BAAA;4BACL,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B;AACF;oBACD;AACD;;gBAGD,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE;oBAC5C,IAAI,CAAC,OAAO,EAAE;AACZ,wBAAA,OAAO,IAAI;AACZ;AAAM,yBAAA;wBACL,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBAC5B;AACD;AACF;AAED,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;AAC1B,gBAAA,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;gBAElB,IAAI,OAAO,GAAG,KAAK;gBACnB,OAAO,CAAC,KAAK,MAAM,EAAE;oBACnB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;wBAC1B,OAAO,GAAG,IAAI;wBACd;AACD;oBACD,CAAC,IAAI,MAAM;AACZ;gBAED,IAAI,CAAC,OAAO,EAAE;oBACZ,IAAI,CAAC,OAAO,EAAE;AACZ,wBAAA,OAAO,IAAI;AACZ;AAAM,yBAAA;wBACL,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBAC5B;AACD;AACF;AACF;AACF;AAED,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,SAAS;AACjB;AAAM,aAAA;AACL,YAAA,OAAO,KAAK;AACb;;IAGH,SAAS,CAAC,MAAc,EAAE,UAAkB,EAAA;QAC1C,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;AACtD;AAAM,aAAA;AACL,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;AACtD;;AAGK,IAAA,eAAe,CAAC,KAAY,EAAA;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QACjC,OAAO,MAAM,KAAK,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC;;IAGzE,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;;IAGhC,UAAU,CAAC,MAAc,EAAE,UAAiB,EAAA;QAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;IAGjD,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC;;IAGzC,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE;;IAGvB,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,KAAK,CAAC;;IAGrD,WAAW,GAAA;AACT,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,KAAK,CAAC;;IAGtD,sBAAsB,GAAA;AACpB;;;;;;AAMG;AACH,QAAA,MAAM,MAAM,GAAgC;AAC1C,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;SACL;QACD,MAAM,OAAO,GAAG,EAAE;QAClB,IAAI,SAAS,GAAG,CAAC;QACjB,IAAI,WAAW,GAAG,CAAC;AAEnB,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;YACvC,WAAW,GAAG,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC;YACnC,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,CAAC,IAAI,CAAC;gBACN;AACD;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5B,YAAA,IAAI,KAAK,EAAE;gBACT,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AACtE,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;AACzB,oBAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1B;AACD,gBAAA,SAAS,EAAE;AACZ;AACF;;QAGD,IAAI,SAAS,KAAK,CAAC,EAAE;AACnB,YAAA,OAAO,IAAI;AACZ;AAAM,aAAA;;AAEL,QAAA,SAAS,KAAK,CAAC;AACf,aAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAC9C;AACA,YAAA,OAAO,IAAI;AACZ;aAAM,IAAI,SAAS,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;;YAE3C,IAAI,GAAG,GAAG,CAAC;AACX,YAAA,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC5B,gBAAA,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC;AAClB;AACD,YAAA,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE;AAC5B,gBAAA,OAAO,IAAI;AACZ;AACF;AAED,QAAA,OAAO,KAAK;;IAGd,qBAAqB,GAAA;QACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;;IAGhD,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,UAAU,IAAI,GAAG,CAAA;;IAG/B,MAAM,GAAA;AACJ,QAAA,QACE,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,IAAI,CAAC,qBAAqB,EAAE;;IAIhC,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;;AA2D5C,IAAA,KAAK,CAAC,EACJ,OAAO,GAAG,KAAK,EACf,MAAM,GAAG,SAAS,EAClB,KAAK,GAAG,SAAS,MAC8C,EAAE,EAAA;AACjE,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAE5C,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACjD;AAAM,aAAA;AACL,YAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACzD;;AAGK,IAAA,MAAM,CAAC,EACb,KAAK,GAAG,IAAI,EACZ,KAAK,GAAG,SAAS,EACjB,MAAM,GAAG,SAAS,MAKhB,EAAE,EAAA;AACJ,QAAA,MAAM,SAAS,GAAG,MAAM,GAAI,MAAM,CAAC,WAAW,EAAa,GAAG,SAAS;AACvE,QAAA,MAAM,QAAQ,GAAG,KAAK,EAAE,WAAW,EAAE;QAErC,MAAM,KAAK,GAAmB,EAAE;AAChC,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;AACrB,QAAA,MAAM,IAAI,GAAG,SAAS,CAAC,EAAE,CAAC;AAE1B,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC,EAAE;AACzB,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,EAAE;QACxB,IAAI,YAAY,GAAG,KAAK;;AAGxB,QAAA,IAAI,SAAS,EAAE;;AAEb,YAAA,IAAI,EAAE,SAAS,IAAI,IAAI,CAAC,EAAE;AACxB,gBAAA,OAAO,EAAE;AACV;AAAM,iBAAA;AACL,gBAAA,WAAW,GAAG,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC1C,YAAY,GAAG,IAAI;AACpB;AACF;QAED,KAAK,IAAI,IAAI,GAAG,WAAW,EAAE,IAAI,IAAI,UAAU,EAAE,IAAI,EAAE,EAAE;;YAEvD,IAAI,IAAI,GAAG,IAAI,EAAE;gBACf,IAAI,IAAI,CAAC;gBACT;AACD;;AAGD,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE;gBAC1D;AACD;YACD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AAElC,YAAA,IAAI,EAAU;YACd,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,gBAAA,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI;oBAAE;;gBAGnC,EAAE,GAAG,IAAI,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/B,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;oBACpB,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC;;oBAGlC,EAAE,GAAG,IAAI,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/B,oBAAA,IAAI,WAAW,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;AACtD,wBAAA,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC;AAC7D;AACF;;gBAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC1B,EAAE,GAAG,IAAI,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC/B,IAAI,EAAE,GAAG,IAAI;wBAAE;oBAEf,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,IAAI,EAAE;wBACnC,OAAO,CACL,KAAK,EACL,EAAE,EACF,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EACpB,IAAI,CAAC,OAAO,CACb;AACF;AAAM,yBAAA,IAAI,EAAE,KAAK,IAAI,CAAC,SAAS,EAAE;AAChC,wBAAA,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC;AAC1D;AACF;AACF;AAAM,iBAAA;AACL,gBAAA,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI;oBAAE;gBAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;oBAC9D,MAAM,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACrC,EAAE,GAAG,IAAI;AAET,oBAAA,OAAO,IAAI,EAAE;wBACX,EAAE,IAAI,MAAM;wBACZ,IAAI,EAAE,GAAG,IAAI;4BAAE;AAEf,wBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;4BACpB,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC;AACnC;AAAM,6BAAA;;4BAEL,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;gCAAE;4BAElC,OAAO,CACL,KAAK,EACL,EAAE,EACF,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EACpB,IAAI,CAAC,OAAO,CACb;4BACD;AACD;;AAGD,wBAAA,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI;4BAAE;AACvC;AACF;AACF;AACF;AAED;;;;AAIG;AAEH,QAAA,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,EAAE;YAC/C,IAAI,CAAC,YAAY,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;;gBAEnD,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;oBAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;AACpC,oBAAA,MAAM,UAAU,GAAG,YAAY,GAAG,CAAC;oBAEnC,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;AAC9B,wBAAA,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;AACxB,wBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;wBACtC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,GAAG,CAAC,CAAC;wBACvC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,EACjC;wBACA,OAAO,CACL,KAAK,EACL,EAAE,EACF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EACf,UAAU,EACV,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,YAAY,CAClB;AACF;AACF;;gBAGD,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;oBAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;AACpC,oBAAA,MAAM,UAAU,GAAG,YAAY,GAAG,CAAC;oBAEnC,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;AAC9B,wBAAA,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;AAC9B,wBAAA,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;AAC9B,wBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;wBACtC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,GAAG,CAAC,CAAC;wBACvC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,EACjC;wBACA,OAAO,CACL,KAAK,EACL,EAAE,EACF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EACf,UAAU,EACV,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,YAAY,CAClB;AACF;AACF;AACF;AACF;AAED;;;AAGG;AACH,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE;AACpC,YAAA,OAAO,KAAK;AACb;;QAGD,MAAM,UAAU,GAAG,EAAE;AAErB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACxB,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE;gBAC7B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B;YACD,IAAI,CAAC,SAAS,EAAE;AACjB;AAED,QAAA,OAAO,UAAU;;IAGnB,IAAI,CACF,IAAsE,EACtE,EAAE,MAAM,GAAG,KAAK,KAA2B,EAAE,EAAA;AAE7C;;;;;;;;;;;;AAYG;QAEH,IAAI,OAAO,GAAG,IAAI;AAElB,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC;AAC1C;aAAM,IAAI,IAAI,KAAK,IAAI,EAAE;YACxB,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;AAClD;AAAM,aAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACnC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;;AAG3B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChD,gBAAA,IACE,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBACtC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;qBACjC,EAAE,WAAW,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EACrE;AACA,oBAAA,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;oBAClB;AACD;AACF;AACF;;QAGD,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,gBAAA,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,CAAA,CAAE,CAAC;AACzC;AAAM,iBAAA;AACL,gBAAA,MAAM,IAAI,KAAK,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAE,CAAA,CAAC;AACzD;AACF;;AAGD,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE;AACpD,YAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;AACvD;AAED;;;AAGG;QACH,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AAE1C,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE;AACxB,QAAA,OAAO,UAAU;;AAGX,IAAA,KAAK,CAAC,IAAkB,EAAA;AAC9B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB,IAAI;AACJ,YAAA,KAAK,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;YAC7C,IAAI,EAAE,IAAI,CAAC,KAAK;AAChB,YAAA,QAAQ,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE;YACtD,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,UAAU,EAAE,IAAI,CAAC,WAAW;AAC7B,SAAA,CAAC;;IAGI,UAAU,CAAC,IAAY,EAAE,EAAU,EAAA;QACzC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AAElC,QAAA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACnC,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QAExB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;;AAG1B,IAAA,SAAS,CAAC,IAAkB,EAAA;AAClC,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;AACrB,QAAA,MAAM,IAAI,GAAG,SAAS,CAAC,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAEhB,QAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE;YAC/B,IAAI,EAAE,KAAK,KAAK,EAAE;gBAChB,IAAI,CAAC,WAAW,EAAE;AACnB;YACD,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AAEjB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;YAEtB;AACD;AAED,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;AAC3B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;QAEjC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;AACtC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;;AAGnC,QAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE;AAChC,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;gBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AAC1B;AAAM,iBAAA;gBACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AAC1B;AACF;;QAGD,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;AACpB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AACxD;;AAGD,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;YACtC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;;AAGzB,YAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;AAClC,gBAAA,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;AAC9B,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;AAChC,gBAAA,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,UAAU,CAAC;AAC1C;AAAM,iBAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;AACzC,gBAAA,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;AAC9B,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;AAChC,gBAAA,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,UAAU,CAAC;AAC1C;;AAGD,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;AACvB;;AAGD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACpD,gBAAA,IACE,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;AACjC,oBAAA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EACtC;AACA,oBAAA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;oBACvC;AACD;AACF;AACF;;AAGD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACtD,gBAAA,IACE,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;AACjC,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAC1C;AACA,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;oBAC3C;AACD;AACF;AACF;AAED,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;;AAGjC,QAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;AAC9B,YAAA,IAAI,QAAQ;YAEZ,IAAI,EAAE,KAAK,KAAK,EAAE;AAChB,gBAAA,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE;AACxB;AAAM,iBAAA;AACL,gBAAA,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE;AACxB;AAED,YAAA,IACE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC;AACtB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,KAAK,IAAI;AACvC,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI;iBACzC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC;AACtB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,KAAK,IAAI;AACvC,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAC3C;AACA,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,gBAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;AAC5B;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACvB;AACF;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACvB;;AAGD,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;AACvB,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC;AACpB;AAAM,aAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE;AACxD,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC;AACpB;AAAM,aAAA;YACL,IAAI,CAAC,UAAU,EAAE;AAClB;QAED,IAAI,EAAE,KAAK,KAAK,EAAE;YAChB,IAAI,CAAC,WAAW,EAAE;AACnB;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,QAAA,IAAI,CAAC,KAAK,IAAI,QAAQ;;IAGxB,IAAI,GAAA;AACF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK;AACvB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE;AAC7B,QAAA,IAAI,IAAI,EAAE;YACR,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AACvC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AAC5B,YAAA,OAAO,UAAU;AAClB;AACD,QAAA,OAAO,IAAI;;IAGL,SAAS,GAAA;QACf,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;QAC/B,IAAI,GAAG,KAAK,SAAS,EAAE;AACrB,YAAA,OAAO,IAAI;AACZ;AAED,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;AAC3B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;AAEjC,QAAA,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI;AAErB,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK;AACvB,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,QAAQ;AAC7B,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,QAAQ;AAC7B,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,SAAS;AAC/B,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,UAAU;AAEjC,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;AAC3B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;AACjC,QAAA,IAAI,CAAC,KAAK,IAAI,QAAQ;AAEtB,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK;AACrB,QAAA,MAAM,IAAI,GAAG,SAAS,CAAC,EAAE,CAAC;AAE1B,QAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE;AAC/B,YAAA,OAAO,IAAI;AACZ;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;;QAGnC,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AACtD;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE;;AAEhC,gBAAA,IAAI,KAAa;gBACjB,IAAI,EAAE,KAAK,KAAK,EAAE;AAChB,oBAAA,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AAAM,qBAAA;AACL,oBAAA,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACD,gBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAC9C;AAAM,iBAAA;;AAEL,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AACzD;AACF;AAED,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE;YACxD,IAAI,UAAkB,EAAE,YAAoB;AAC5C,YAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;AAClC,gBAAA,UAAU,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;AACxB,gBAAA,YAAY,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;AAC3B;AAAM,iBAAA;AACL,gBAAA,UAAU,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;AACxB,gBAAA,YAAY,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;AAC3B;AACD,YAAA,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,UAAU,CAAC;AAC1C;AAED,QAAA,OAAO,IAAI;;IAGb,GAAG,CAAC,EACF,OAAO,GAAG,IAAI,EACd,QAAQ,GAAG,CAAC,GAAA,GAC+B,EAAE,EAAA;AAC7C;;;AAGG;QAEH,MAAM,MAAM,GAAa,EAAE;QAC3B,IAAI,YAAY,GAAG,KAAK;;AAGxB,QAAA,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;AAC5B;;;;;;AAMG;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AACjC,YAAA,IAAI,SAAS;AAAE,gBAAA,MAAM,CAAC,IAAI,CAAC,CAAI,CAAA,EAAA,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA,EAAA,CAAI,GAAG,OAAO,CAAC;YACnE,YAAY,GAAG,IAAI;AACpB;AAED,QAAA,IAAI,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACxC,YAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AACrB;AAED,QAAA,MAAM,aAAa,GAAG,CAAC,UAAkB,KAAI;YAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAC1C,YAAA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;AAClC,gBAAA,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAClD,UAAU,GAAG,GAAG,UAAU,CAAA,EAAG,SAAS,CAAI,CAAA,EAAA,OAAO,GAAG;AACrD;AACD,YAAA,OAAO,UAAU;AACnB,SAAC;;QAGD,MAAM,eAAe,GAAG,EAAE;AAC1B,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AACvC;QAED,MAAM,KAAK,GAAG,EAAE;QAChB,IAAI,UAAU,GAAG,EAAE;;AAGnB,QAAA,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AAC9B;;AAGD,QAAA,OAAO,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,YAAA,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;AACtC,YAAA,MAAM,IAAI,GAAG,eAAe,CAAC,GAAG,EAAE;;YAGlC,IAAI,CAAC,IAAI,EAAE;gBACT;AACD;;AAGD,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,EAAE;AAC/C,gBAAA,MAAM,MAAM,GAAG,CAAA,EAAG,IAAI,CAAC,WAAW,OAAO;;AAEzC,gBAAA,UAAU,GAAG,UAAU,GAAG,CAAG,EAAA,UAAU,CAAI,CAAA,EAAA,MAAM,CAAE,CAAA,GAAG,MAAM;AAC7D;AAAM,iBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,EAAE;;gBAE7B,IAAI,UAAU,CAAC,MAAM,EAAE;AACrB,oBAAA,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AACvB;AACD,gBAAA,UAAU,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG;AACpC;YAED,UAAU;gBACR,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACrB;;QAGD,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACtC;;QAGD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC;AAEtC;;;AAGG;QACH,IAAI,QAAQ,KAAK,CAAC,EAAE;AAClB,YAAA,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AACzC;;AAGD,QAAA,MAAM,KAAK,GAAG,YAAA;AACZ,YAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC1D,MAAM,CAAC,GAAG,EAAE;AACZ,gBAAA,OAAO,IAAI;AACZ;AACD,YAAA,OAAO,KAAK;AACd,SAAC;;AAGD,QAAA,MAAM,WAAW,GAAG,UAAU,KAAa,EAAE,IAAY,EAAA;YACvD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBACnC,IAAI,CAAC,KAAK,EAAE;oBACV;AACD;AACD,gBAAA,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,QAAQ,EAAE;oBACnC,OAAO,KAAK,EAAE,EAAE;AACd,wBAAA,KAAK,EAAE;AACR;AACD,oBAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;oBACpB,KAAK,GAAG,CAAC;AACV;AACD,gBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AAClB,gBAAA,KAAK,IAAI,KAAK,CAAC,MAAM;AACrB,gBAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;AAChB,gBAAA,KAAK,EAAE;AACR;YACD,IAAI,KAAK,EAAE,EAAE;AACX,gBAAA,KAAK,EAAE;AACR;AACD,YAAA,OAAO,KAAK;AACd,SAAC;;QAGD,IAAI,YAAY,GAAG,CAAC;AACpB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,QAAQ,EAAE;gBAC7C,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC1B,YAAY,GAAG,WAAW,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;oBAClD;AACD;AACF;;AAED,YAAA,IAAI,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;;gBAExD,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;oBACrC,MAAM,CAAC,GAAG,EAAE;AACb;AAED,gBAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;gBACpB,YAAY,GAAG,CAAC;AACjB;iBAAM,IAAI,CAAC,KAAK,CAAC,EAAE;AAClB,gBAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;AAChB,gBAAA,YAAY,EAAE;AACf;YACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrB,YAAA,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;AAChC;AAED,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;;AAGxB;;AAEG;IACH,MAAM,CAAC,GAAG,IAAc,EAAA;AACtB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACvC,YAAA,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;AAClE,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACpC;AACF;QACD,OAAO,IAAI,CAAC,OAAO;;;IAIrB,SAAS,CAAC,GAAW,EAAE,KAAa,EAAA;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,IAAI;AAC1D,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE;;AAG1B,IAAA,YAAY,CAAC,GAAW,EAAA;AACtB,QAAA,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;AACvB,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,IAAI;AACjD,YAAA,OAAO,IAAI;AACZ;AACD,QAAA,OAAO,KAAK;;;IAId,UAAU,GAAA;QACR,MAAM,cAAc,GAA2B,EAAE;AACjD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACvD,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,gBAAA,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK;AAC5B;AACF;AACD,QAAA,OAAO,cAAc;;AAGvB,IAAA,OAAO,CACL,GAAW,EACX,EACE,MAAM,GAAG,KAAK,EACd,WAAW,GAAG,OAAO,GAAA,GACyB,EAAE,EAAA;;QAGlD,IAAI,WAAW,KAAK,OAAO,EAAE;AAC3B,YAAA,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;AACtD;AAED,QAAA,MAAM,SAAS,GAAGA,SAAK,CAAC,GAAG,CAAC;;QAG5B,IAAI,CAAC,KAAK,EAAE;;AAGZ,QAAA,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO;QACjC,IAAI,GAAG,GAAG,EAAE;AAEZ,QAAA,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;;AAEzB,YAAA,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE;AAC/B,gBAAA,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACnB;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B;AAED;;;AAGG;QACH,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;AAC1C;AACF;AAAM,aAAA;AACL;;;AAGG;AACH,YAAA,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE;AAC5B,gBAAA,IAAI,EAAE,KAAK,IAAI,OAAO,CAAC,EAAE;AACvB,oBAAA,MAAM,IAAI,KAAK,CACb,sDAAsD,CACvD;AACF;;AAED,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;AACrD;AACF;AAED,QAAA,IAAI,IAAI,GAAG,SAAS,CAAC,IAAI;AAEzB,QAAA,OAAO,IAAI,EAAE;YACX,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,gBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;gBAEjD,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,MAAM,IAAI,KAAK,CAAC,CAAA,qBAAA,EAAwB,IAAI,CAAC,IAAI,CAAE,CAAA,CAAC;AACrD;AAAM,qBAAA;AACL,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;oBACpB,IAAI,CAAC,iBAAiB,EAAE;AACzB;AACF;AAED,YAAA,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;AAC9B,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO;AAC1C;AAED,YAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1B;AAED;;;;AAIG;AAEH,QAAA,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM;AAC/B,QAAA,IACE,MAAM;YACN,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM;AAChC,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,MAAM,EACjC;AACA,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;AACjC;;AAGH;;;;;;;;;;AAUG;IAEK,UAAU,CAAC,IAAkB,EAAE,KAAqB,EAAA;QAC1D,IAAI,MAAM,GAAG,EAAE;AAEf,QAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;YAClC,MAAM,GAAG,KAAK;AACf;AAAM,aAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;YACzC,MAAM,GAAG,OAAO;AACjB;AAAM,aAAA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE;AACtC,YAAA,OAAO,YAAY;AACpB;AAAM,aAAA;AACL,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;gBACvB,MAAM,aAAa,GAAG,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;gBACnD,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,aAAa;AACnD;AAED,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE;AACjD,gBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;oBACvB,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClC;gBACD,MAAM,IAAI,GAAG;AACd;AAED,YAAA,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YAE5B,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAC7C;AACF;AAED,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACpB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AAClB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;gBACtB,MAAM,IAAI,GAAG;AACd;AAAM,iBAAA;gBACL,MAAM,IAAI,GAAG;AACd;AACF;QACD,IAAI,CAAC,SAAS,EAAE;AAEhB,QAAA,OAAO,MAAM;;;AAIP,IAAA,YAAY,CAAC,IAAY,EAAE,MAAM,GAAG,KAAK,EAAA;;AAE/C,QAAA,IAAI,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC;QAEjC,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,SAAS,KAAK,KAAK,EAAE;gBACvB,SAAS,GAAG,KAAK;AAClB;iBAAM,IAAI,SAAS,KAAK,OAAO,EAAE;gBAChC,SAAS,GAAG,OAAO;AACpB;AACF;;QAGD,IAAI,SAAS,IAAI,YAAY,EAAE;AAC7B,YAAA,MAAM,GAAG,GAAiB;gBACxB,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,EAAE,EAAE,CAAC;AACL,gBAAA,KAAK,EAAE,GAAG;gBACV,KAAK,EAAE,IAAI,CAAC,SAAS;aACtB;AACD,YAAA,OAAO,GAAG;AACX;AAED,QAAA,IAAI,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;AACzC,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;;AAG1D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChD,YAAA,IAAI,SAAS,KAAK,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;AAC/D,gBAAA,OAAO,KAAK,CAAC,CAAC,CAAC;AAChB;AACF;;AAGD,QAAA,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,IAAI;AACZ;QAED,IAAI,KAAK,GAAG,SAAS;QACrB,IAAI,OAAO,GAAG,SAAS;QACvB,IAAI,IAAI,GAAG,SAAS;QACpB,IAAI,EAAE,GAAG,SAAS;QAClB,IAAI,SAAS,GAAG,SAAS;AAEzB;;;;;;;;;;;;;;;AAeG;QAEH,IAAI,mBAAmB,GAAG,KAAK;AAE/B,QAAA,OAAO,GAAG,SAAS,CAAC,KAAK,CACvB,4DAA4D,CAE7D;AAED,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC;AAClB,YAAA,IAAI,GAAG,OAAO,CAAC,CAAC,CAAW;AAC3B,YAAA,EAAE,GAAG,OAAO,CAAC,CAAC,CAAW;AACzB,YAAA,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC;AAEtB,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;gBACpB,mBAAmB,GAAG,IAAI;AAC3B;AACF;AAAM,aAAA;AACL;;;;;AAKG;AAEH,YAAA,OAAO,GAAG,SAAS,CAAC,KAAK,CACvB,8DAA8D,CAC/D;AAED,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC;AAClB,gBAAA,IAAI,GAAG,OAAO,CAAC,CAAC,CAAW;AAC3B,gBAAA,EAAE,GAAG,OAAO,CAAC,CAAC,CAAW;AACzB,gBAAA,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC;AAEtB,gBAAA,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;oBACpB,mBAAmB,GAAG,IAAI;AAC3B;AACF;AACF;AAED,QAAA,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;AACrC,QAAA,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAClB,YAAA,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,KAAK,GAAI,KAAqB,GAAG,SAAS;AAClD,SAAA,CAAC;QAEF,IAAI,CAAC,EAAE,EAAE;AACP,YAAA,OAAO,IAAI;AACZ;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,IAAI,EAAE;;AAET,gBAAA,IACE,SAAS;oBACT,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAC9D;AACA,oBAAA,OAAO,KAAK,CAAC,CAAC,CAAC;AAChB;;AAEF;AAAM,iBAAA,IACL,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;gBAChD,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC3B,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACvB,iBAAC,CAAC,SAAS,IAAI,SAAS,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAC7D;AACA,gBAAA,OAAO,KAAK,CAAC,CAAC,CAAC;AAChB;AAAM,iBAAA,IAAI,mBAAmB,EAAE;AAC9B;;;AAGG;gBAEH,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACvC,gBAAA,IACE,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;oBAChD,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACvB,qBAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACxC,qBAAC,CAAC,SAAS,IAAI,SAAS,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAC7D;AACA,oBAAA,OAAO,KAAK,CAAC,CAAC,CAAC;AAChB;AACF;AACF;AAED,QAAA,OAAO,IAAI;;IAGb,KAAK,GAAA;QACH,IAAI,CAAC,GAAG,iCAAiC;AACzC,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;;AAEvC,YAAA,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AACjB,gBAAA,CAAC,IAAI,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACtC;AAED,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBAClB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;gBACjC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK;AAClC,gBAAA,MAAM,MAAM,GACV,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE;AAC7D,gBAAA,CAAC,IAAI,GAAG,GAAG,MAAM,GAAG,GAAG;AACxB;AAAM,iBAAA;gBACL,CAAC,IAAI,KAAK;AACX;AAED,YAAA,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;gBAClB,CAAC,IAAI,KAAK;gBACV,CAAC,IAAI,CAAC;AACP;AACF;QACD,CAAC,IAAI,iCAAiC;QACtC,CAAC,IAAI,6BAA6B;AAElC,QAAA,OAAO,CAAC;;AAGV,IAAA,KAAK,CAAC,KAAa,EAAA;AACjB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;QAC3C,IAAI,KAAK,GAAG,CAAC;AACb,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;AAExB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACxB,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;AAChC,gBAAA,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE;oBACjB,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC/B;AAAM,qBAAA;AACL,oBAAA,KAAK,EAAE;AACR;AACF;YACD,IAAI,CAAC,SAAS,EAAE;AACjB;AAED,QAAA,OAAO,KAAK;;AAGd,IAAA,OAAO,CAAC,KAAY,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,EAAE;AACvB,YAAA,OAAO,KAAK;AACb;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACf,QAAA,OAAO,IAAI;;IAGb,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,KAAK;;IAGnB,KAAK,GAAA;QACH,MAAM,MAAM,GAAG,EAAE;QACjB,IAAI,GAAG,GAAG,EAAE;AAEZ,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AAC1B,gBAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AACf;AAAM,iBAAA;gBACL,GAAG,CAAC,IAAI,CAAC;AACP,oBAAA,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;oBACpB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;oBACzB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK;AAC5B,iBAAA,CAAC;AACH;AACD,YAAA,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;AAClB,gBAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;gBAChB,GAAG,GAAG,EAAE;gBACR,CAAC,IAAI,CAAC;AACP;AACF;AAED,QAAA,OAAO,MAAM;;AAGf,IAAA,WAAW,CAAC,MAAc,EAAA;QACxB,IAAI,MAAM,IAAI,IAAI,EAAE;AAClB,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM;AAC1D;AAED,QAAA,OAAO,IAAI;;AAOb,IAAA,OAAO,CAAC,EAAE,OAAO,GAAG,KAAK,KAA4B,EAAE,EAAA;QACrD,MAAM,eAAe,GAAG,EAAE;QAC1B,MAAM,WAAW,GAAG,EAAE;AAEtB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AACvC;AAED,QAAA,OAAO,IAAI,EAAE;AACX,YAAA,MAAM,IAAI,GAAG,eAAe,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,IAAI,EAAE;gBACT;AACD;AAED,YAAA,IAAI,OAAO,EAAE;gBACX,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACvC;AAAM,iBAAA;AACL,gBAAA,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AACvD;AACD,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACrB;AAED,QAAA,OAAO,WAAW;;AAGpB;;;AAGG;AACK,IAAA,iBAAiB,CAAC,IAAY,EAAA;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGnC,iBAAiB,GAAA;QACvB,IAAI,CAAC,cAAc,CAAC,GAAG,CACrB,IAAI,CAAC,KAAK,EACV,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/C;;AAGK,IAAA,iBAAiB,CAAC,IAAY,EAAA;AACpC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAEvD,IAAI,YAAY,KAAK,CAAC,EAAE;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;AACjC;AAAM,aAAA;YACL,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,GAAG,CAAC,CAAC;AAChD;;IAGK,cAAc,GAAA;QACpB,MAAM,eAAe,GAAG,EAAE;QAC1B,MAAM,eAAe,GAA2B,EAAE;AAElD,QAAA,MAAM,WAAW,GAAG,CAAC,GAAW,KAAI;AAClC,YAAA,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;gBACzB,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;AAC3C;AACH,SAAC;AAED,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AACvC;AAED,QAAA,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAEvB,QAAA,OAAO,IAAI,EAAE;AACX,YAAA,MAAM,IAAI,GAAG,eAAe,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,IAAI,EAAE;gBACT;AACD;AACD,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACpB,YAAA,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACxB;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,eAAe;;IAGlC,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;;AAGnC,IAAA,UAAU,CAAC,OAAe,EAAA;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;;AAG1E;;AAEG;IACH,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE;;IAG7B,aAAa,GAAA;QACX,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACjC,QAAA,OAAO,OAAO;;IAGhB,WAAW,GAAA;QACT,IAAI,CAAC,cAAc,EAAE;AACrB,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,KAAI;AACrD,YAAA,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;AACnD,SAAC,CAAC;;AAGJ;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE;;IAG9B,cAAc,GAAA;QACZ,IAAI,CAAC,cAAc,EAAE;AACrB,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAI;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;AACnC,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YAC1B,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;AACvC,SAAC,CAAC;;IAGJ,iBAAiB,CACf,KAAY,EACZ,MAA4D,EAAA;QAE5D,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAU,EAAE;AACzC,YAAA,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AAC9B,gBAAA,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;oBAChB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC;AACrC;AAAM,qBAAA;oBACL,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACtC;AACF;AACF;QAED,IAAI,CAAC,qBAAqB,EAAE;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;AAE5C,QAAA,QACE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC;AAC5D,aAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;;AAIpE,IAAA,iBAAiB,CAAC,KAAY,EAAA;QAC5B,OAAO;AACL,YAAA,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;AACnD,YAAA,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;SACtD;;IAGH,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,WAAW;;AAE1B;;;;;;;;;;;;;;;;;;"}