#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
require("../server/require-hook");
const _commander = require("next/dist/compiled/commander");
const _log = require("../build/output/log");
const _semver = /*#__PURE__*/ _interop_require_default(require("next/dist/compiled/semver"));
const _picocolors = require("../lib/picocolors");
const _formatclihelpoutput = require("../lib/format-cli-help-output");
const _constants = require("../lib/constants");
const _utils = require("../server/lib/utils");
const _nexttest = require("../cli/next-test.js");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
if (process.env.NEXT_RSPACK) {
    // silent rspack's schema check
    process.env.RSPACK_CONFIG_VALIDATE = 'loose-silent';
}
if (!_semver.default.satisfies(process.versions.node, "^18.18.0 || ^19.8.0 || >= 20.0.0", {
    includePrerelease: true
})) {
    console.error(`You are using Node.js ${process.versions.node}. For Next.js, Node.js version "${"^18.18.0 || ^19.8.0 || >= 20.0.0"}" is required.`);
    process.exit(1);
}
// Start performance profiling after Node.js version is checked
performance.mark('next-start');
for (const dependency of [
    'react',
    'react-dom'
]){
    try {
        // When 'npm link' is used it checks the clone location. Not the project.
        require.resolve(dependency);
    } catch (err) {
        console.warn(`The module '${dependency}' was not found. Next.js requires that you include it in 'dependencies' of your 'package.json'. To add it, run 'npm install ${dependency}'`);
    }
}
class NextRootCommand extends _commander.Command {
    createCommand(name) {
        const command = new _commander.Command(name);
        command.addOption(new _commander.Option('--inspect').hideHelp());
        command.hook('preAction', (event)=>{
            const commandName = event.name();
            const defaultEnv = commandName === 'dev' ? 'development' : 'production';
            const standardEnv = [
                'production',
                'development',
                'test'
            ];
            if (process.env.NODE_ENV) {
                const isNotStandard = !standardEnv.includes(process.env.NODE_ENV);
                const shouldWarnCommands = process.env.NODE_ENV === 'development' ? [
                    'start',
                    'build'
                ] : process.env.NODE_ENV === 'production' ? [
                    'dev'
                ] : [];
                if (isNotStandard || shouldWarnCommands.includes(commandName)) {
                    (0, _log.warn)(_constants.NON_STANDARD_NODE_ENV);
                }
            }
            ;
            process.env.NODE_ENV = process.env.NODE_ENV || defaultEnv;
            process.env.NEXT_RUNTIME = 'nodejs';
            if (event.getOptionValue('inspect') === true) {
                console.error(`\`--inspect\` flag is deprecated. Use env variable NODE_OPTIONS instead: NODE_OPTIONS='--inspect' next ${commandName}`);
                process.exit(1);
            }
        });
        return command;
    }
}
const program = new NextRootCommand();
program.name('next').description('The Next.js CLI allows you to develop, build, start your application, and more.').configureHelp({
    formatHelp: (cmd, helper)=>(0, _formatclihelpoutput.formatCliHelpOutput)(cmd, helper),
    subcommandTerm: (cmd)=>`${cmd.name()} ${cmd.usage()}`
}).helpCommand(false).helpOption('-h, --help', 'Displays this message.').version(`Next.js v${"15.3.5"}`, '-v, --version', 'Outputs the Next.js version.');
program.command('build').description('Creates an optimized production build of your application. The output displays information about each route.').argument('[directory]', `A directory on which to build the application. ${(0, _picocolors.italic)('If no directory is provided, the current directory will be used.')}`).option('-d, --debug', 'Enables a more verbose build output.').option('--no-lint', 'Disables linting.').option('--no-mangling', 'Disables mangling.').option('--profile', 'Enables production profiling for React.').option('--experimental-app-only', 'Builds only App Router routes.').option('--turbo', 'Starts development mode using Turbopack.').option('--turbopack', 'Starts development mode using Turbopack.').addOption(new _commander.Option('--experimental-build-mode [mode]', 'Uses an experimental build mode.').choices([
    'compile',
    'generate',
    'generate-env'
]).default('default')).option('--experimental-debug-memory-usage', 'Enables memory profiling features to debug memory consumption.').option('--experimental-upload-trace, <traceUrl>', 'Reports a subset of the debugging trace to a remote HTTP URL. Includes sensitive data.').action((directory, options)=>// ensure process exits after build completes so open handles/connections
    // don't cause process to hang
    import('../cli/next-build.js').then((mod)=>mod.nextBuild(options, directory).then(()=>process.exit(0)))).usage('[directory] [options]');
program.command('dev', {
    isDefault: true
}).description('Starts Next.js in development mode with hot-code reloading, error reporting, and more.').argument('[directory]', `A directory on which to build the application. ${(0, _picocolors.italic)('If no directory is provided, the current directory will be used.')}`).option('--turbo', 'Starts development mode using Turbopack.').option('--turbopack', 'Starts development mode using Turbopack.').addOption(new _commander.Option('-p, --port <port>', 'Specify a port number on which to start the application.').argParser(_utils.parseValidPositiveInteger).default(3000).env('PORT')).option('-H, --hostname <hostname>', 'Specify a hostname on which to start the application (default: 0.0.0.0).').option('--disable-source-maps', "Don't start the Dev server with `--enable-source-maps`.", false).option('--experimental-https', 'Starts the server with HTTPS and generates a self-signed certificate.').option('--experimental-https-key, <path>', 'Path to a HTTPS key file.').option('--experimental-https-cert, <path>', 'Path to a HTTPS certificate file.').option('--experimental-https-ca, <path>', 'Path to a HTTPS certificate authority file.').option('--experimental-upload-trace, <traceUrl>', 'Reports a subset of the debugging trace to a remote HTTP URL. Includes sensitive data.').action((directory, options, { _optionValueSources })=>{
    const portSource = _optionValueSources.port;
    import('../cli/next-dev.js').then((mod)=>mod.nextDev(options, portSource, directory));
}).usage('[directory] [options]');
program.command('export', {
    hidden: true
}).action(()=>import('../cli/next-export.js').then((mod)=>mod.nextExport())).helpOption(false);
program.command('info').description('Prints relevant details about the current system which can be used to report Next.js bugs.').addHelpText('after', `\nLearn more: ${(0, _picocolors.cyan)('https://nextjs.org/docs/api-reference/cli#info')}`).option('--verbose', 'Collects additional information for debugging.').action((options)=>import('../cli/next-info.js').then((mod)=>mod.nextInfo(options)));
program.command('lint').description('Runs ESLint for all files in the `/src`, `/app`, `/pages`, `/components`, and `/lib` directories. It also provides a guided setup to install any required dependencies if ESLint is not already configured in your application.').argument('[directory]', `A base directory on which to lint the application. ${(0, _picocolors.italic)('If no directory is provided, the current directory will be used.')}`).option('-d, --dir, <dirs...>', 'Include directory, or directories, to run ESLint.').option('--file, <files...>', 'Include file, or files, to run ESLint.').addOption(new _commander.Option('--ext, [exts...]', 'Specify JavaScript file extensions.').default([
    '.js',
    '.mjs',
    '.cjs',
    '.jsx',
    '.ts',
    '.mts',
    '.cts',
    '.tsx'
])).option('-c, --config, <config>', 'Uses this configuration file, overriding all other configuration options.').option('--resolve-plugins-relative-to, <rprt>', 'Specify a directory where plugins should be resolved from.').option('--strict', 'Creates a `.eslintrc.json` file using the Next.js strict configuration.').option('--rulesdir, <rulesdir...>', 'Uses additional rules from this directory(s).').option('--fix', 'Automatically fix linting issues.').option('--fix-type <fixType>', 'Specify the types of fixes to apply (e.g., problem, suggestion, layout).').option('--ignore-path <path>', 'Specify a file to ignore.').option('--no-ignore', 'Disables the `--ignore-path` option.').option('--quiet', 'Reports errors only.').addOption(new _commander.Option('--max-warnings [maxWarnings]', 'Specify the number of warnings before triggering a non-zero exit code.').argParser(_utils.parseValidPositiveInteger).default(-1)).option('-o, --output-file, <outputFile>', 'Specify a file to write report to.').option('-f, --format, <format>', 'Uses a specific output format.').option('--no-inline-config', 'Prevents comments from changing config or rules.').addOption(new _commander.Option('--report-unused-disable-directives-severity <level>', 'Specify severity level for unused eslint-disable directives.').choices([
    'error',
    'off',
    'warn'
])).option('--no-cache', 'Disables caching.').option('--cache-location, <cacheLocation>', 'Specify a location for cache.').addOption(new _commander.Option('--cache-strategy, [cacheStrategy]', 'Specify a strategy to use for detecting changed files in the cache.').default('metadata')).option('--error-on-unmatched-pattern', 'Reports errors when any file patterns are unmatched.').action((directory, options)=>import('../cli/next-lint.js').then((mod)=>mod.nextLint(options, directory))).usage('[directory] [options]');
program.command('start').description('Starts Next.js in production mode. The application should be compiled with `next build` first.').argument('[directory]', `A directory on which to start the application. ${(0, _picocolors.italic)('If no directory is provided, the current directory will be used.')}`).addOption(new _commander.Option('-p, --port <port>', 'Specify a port number on which to start the application.').argParser(_utils.parseValidPositiveInteger).default(3000).env('PORT')).option('-H, --hostname <hostname>', 'Specify a hostname on which to start the application (default: 0.0.0.0).').addOption(new _commander.Option('--keepAliveTimeout <keepAliveTimeout>', 'Specify the maximum amount of milliseconds to wait before closing inactive connections.').argParser(_utils.parseValidPositiveInteger)).addOption(new _commander.Option('--turbo').hideHelp()).option('--turbopack', 'Starts development mode using Turbopack.').action((directory, options)=>import('../cli/next-start.js').then((mod)=>mod.nextStart(options, directory))).usage('[directory] [options]');
program.command('telemetry').description(`Allows you to enable or disable Next.js' ${(0, _picocolors.bold)('completely anonymous')} telemetry collection.`).addArgument(new _commander.Argument('[arg]').choices([
    'disable',
    'enable',
    'status'
])).addHelpText('after', `\nLearn more: ${(0, _picocolors.cyan)('https://nextjs.org/telemetry')}`).addOption(new _commander.Option('--enable', `Enables Next.js' telemetry collection.`).conflicts('disable')).option('--disable', `Disables Next.js' telemetry collection.`).action((arg, options)=>import('../cli/next-telemetry.js').then((mod)=>mod.nextTelemetry(options, arg)));
program.command('experimental-test').description(`Execute \`next/experimental/testmode\` tests using a specified test runner. The test runner defaults to 'playwright' if the \`experimental.defaultTestRunner\` configuration option or the \`--test-runner\` option are not set.`).argument('[directory]', `A Next.js project directory to execute the test runner on. ${(0, _picocolors.italic)('If no directory is provided, the current directory will be used.')}`).argument('[test-runner-args...]', 'Any additional arguments or options to pass down to the test runner `test` command.').option('--test-runner [test-runner]', `Any supported test runner. Options: ${(0, _picocolors.bold)(_nexttest.SUPPORTED_TEST_RUNNERS_LIST.join(', '))}. ${(0, _picocolors.italic)("If no test runner is provided, the Next.js config option `experimental.defaultTestRunner`, or 'playwright' will be used.")}`).allowUnknownOption().action((directory, testRunnerArgs, options)=>{
    return import('../cli/next-test.js').then((mod)=>{
        mod.nextTest(directory, testRunnerArgs, options);
    });
}).usage('[directory] [options]');
const internal = program.command('internal').description('Internal debugging commands. Use with caution. Not covered by semver.');
internal.command('trace').alias('turbo-trace-server').argument('[file]', 'Trace file to serve.').action((file)=>{
    return import('../cli/internal/turbo-trace-server.js').then((mod)=>mod.startTurboTraceServerCli(file));
});
program.parse(process.argv);

//# sourceMappingURL=next.map