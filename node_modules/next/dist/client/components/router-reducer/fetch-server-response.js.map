{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fetch-server-response.ts"], "sourcesContent": ["'use client'\n\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport type {\n  FlightRouterState,\n  NavigationFlightResponse,\n} from '../../../server/app-render/types'\n\nimport type { NEXT_ROUTER_SEGMENT_PREFETCH_HEADER } from '../app-router-headers'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_URL,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n} from '../app-router-headers'\nimport { callServer } from '../../app-call-server'\nimport { findSourceMapURL } from '../../app-find-source-map-url'\nimport { PrefetchKind } from './router-reducer-types'\nimport {\n  normalizeFlightData,\n  prepareFlightRouterStateForRequest,\n  type NormalizedFlightData,\n} from '../../flight-data-helpers'\nimport { getAppBuildId } from '../../app-build-id'\nimport { setCacheBustingSearchParam } from './set-cache-busting-search-param'\n\nexport interface FetchServerResponseOptions {\n  readonly flightRouterState: FlightRouterState\n  readonly nextUrl: string | null\n  readonly prefetchKind?: PrefetchKind\n  readonly isHmrRefresh?: boolean\n}\n\nexport type FetchServerResponseResult = {\n  flightData: NormalizedFlightData[] | string\n  canonicalUrl: URL | undefined\n  couldBeIntercepted: boolean\n  prerendered: boolean\n  postponed: boolean\n  staleTime: number\n}\n\nexport type RequestHeaders = {\n  [RSC_HEADER]?: '1'\n  [NEXT_ROUTER_STATE_TREE_HEADER]?: string\n  [NEXT_URL]?: string\n  [NEXT_ROUTER_PREFETCH_HEADER]?: '1'\n  [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]?: string\n  'x-deployment-id'?: string\n  [NEXT_HMR_REFRESH_HEADER]?: '1'\n  // A header that is only added in test mode to assert on fetch priority\n  'Next-Test-Fetch-Priority'?: RequestInit['priority']\n}\n\nexport function urlToUrlWithoutFlightMarker(url: string): URL {\n  const urlWithoutFlightParameters = new URL(url, location.origin)\n  urlWithoutFlightParameters.searchParams.delete(NEXT_RSC_UNION_QUERY)\n  if (process.env.NODE_ENV === 'production') {\n    if (\n      process.env.__NEXT_CONFIG_OUTPUT === 'export' &&\n      urlWithoutFlightParameters.pathname.endsWith('.txt')\n    ) {\n      const { pathname } = urlWithoutFlightParameters\n      const length = pathname.endsWith('/index.txt') ? 10 : 4\n      // Slice off `/index.txt` or `.txt` from the end of the pathname\n      urlWithoutFlightParameters.pathname = pathname.slice(0, -length)\n    }\n  }\n  return urlWithoutFlightParameters\n}\n\nfunction doMpaNavigation(url: string): FetchServerResponseResult {\n  return {\n    flightData: urlToUrlWithoutFlightMarker(url).toString(),\n    canonicalUrl: undefined,\n    couldBeIntercepted: false,\n    prerendered: false,\n    postponed: false,\n    staleTime: -1,\n  }\n}\n\nlet abortController = new AbortController()\n\nif (typeof window !== 'undefined') {\n  // Abort any in-flight requests when the page is unloaded, e.g. due to\n  // reloading the page or performing hard navigations. This allows us to ignore\n  // what would otherwise be a thrown TypeError when the browser cancels the\n  // requests.\n  window.addEventListener('pagehide', () => {\n    abortController.abort()\n  })\n\n  // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n  // and the JavaScript execution context is restored by the browser.\n  window.addEventListener('pageshow', () => {\n    abortController = new AbortController()\n  })\n}\n\n/**\n * Fetch the flight data for the provided url. Takes in the current router state\n * to decide what to render server-side.\n */\nexport async function fetchServerResponse(\n  url: URL,\n  options: FetchServerResponseOptions\n): Promise<FetchServerResponseResult> {\n  const { flightRouterState, nextUrl, prefetchKind } = options\n\n  const headers: RequestHeaders = {\n    // Enable flight response\n    [RSC_HEADER]: '1',\n    // Provide the current router state\n    [NEXT_ROUTER_STATE_TREE_HEADER]: prepareFlightRouterStateForRequest(\n      flightRouterState,\n      options.isHmrRefresh\n    ),\n  }\n\n  /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */\n  if (prefetchKind === PrefetchKind.AUTO) {\n    headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n  }\n\n  if (process.env.NODE_ENV === 'development' && options.isHmrRefresh) {\n    headers[NEXT_HMR_REFRESH_HEADER] = '1'\n  }\n\n  if (nextUrl) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  try {\n    // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n    // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n    // Otherwise, all other prefetches are sent with a \"low\" priority.\n    // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n    const fetchPriority = prefetchKind\n      ? prefetchKind === PrefetchKind.TEMPORARY\n        ? 'high'\n        : 'low'\n      : 'auto'\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        // In \"output: export\" mode, we can't rely on headers to distinguish\n        // between HTML and RSC requests. Instead, we append an extra prefix\n        // to the request.\n        url = new URL(url)\n        if (url.pathname.endsWith('/')) {\n          url.pathname += 'index.txt'\n        } else {\n          url.pathname += '.txt'\n        }\n      }\n    }\n\n    const res = await createFetch(\n      url,\n      headers,\n      fetchPriority,\n      abortController.signal\n    )\n\n    const responseUrl = urlToUrlWithoutFlightMarker(res.url)\n    const canonicalUrl = res.redirected ? responseUrl : undefined\n\n    const contentType = res.headers.get('content-type') || ''\n    const interception = !!res.headers.get('vary')?.includes(NEXT_URL)\n    const postponed = !!res.headers.get(NEXT_DID_POSTPONE_HEADER)\n    const staleTimeHeaderSeconds = res.headers.get(\n      NEXT_ROUTER_STALE_TIME_HEADER\n    )\n    const staleTime =\n      staleTimeHeaderSeconds !== null\n        ? parseInt(staleTimeHeaderSeconds, 10) * 1000\n        : -1\n    let isFlightResponse = contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        if (!isFlightResponse) {\n          isFlightResponse = contentType.startsWith('text/plain')\n        }\n      }\n    }\n\n    // If fetch returns something different than flight response handle it like a mpa navigation\n    // If the fetch was not 200, we also handle it like a mpa navigation\n    if (!isFlightResponse || !res.ok || !res.body) {\n      // in case the original URL came with a hash, preserve it before redirecting to the new URL\n      if (url.hash) {\n        responseUrl.hash = url.hash\n      }\n\n      return doMpaNavigation(responseUrl.toString())\n    }\n\n    // We may navigate to a page that requires a different Webpack runtime.\n    // In prod, every page will have the same Webpack runtime.\n    // In dev, the Webpack runtime is minimal for each page.\n    // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n    if (process.env.NODE_ENV !== 'production' && !process.env.TURBOPACK) {\n      await require('../react-dev-overlay/app/hot-reloader-client').waitForWebpackRuntimeHotUpdate()\n    }\n\n    // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n    const flightStream = postponed\n      ? createUnclosingPrefetchStream(res.body)\n      : res.body\n    const response = await (createFromNextReadableStream(\n      flightStream\n    ) as Promise<NavigationFlightResponse>)\n\n    if (getAppBuildId() !== response.b) {\n      return doMpaNavigation(res.url)\n    }\n\n    return {\n      flightData: normalizeFlightData(response.f),\n      canonicalUrl: canonicalUrl,\n      couldBeIntercepted: interception,\n      prerendered: response.S,\n      postponed,\n      staleTime,\n    }\n  } catch (err) {\n    if (!abortController.signal.aborted) {\n      console.error(\n        `Failed to fetch RSC payload for ${url}. Falling back to browser navigation.`,\n        err\n      )\n    }\n\n    // If fetch fails handle it like a mpa navigation\n    // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n    // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n    return {\n      flightData: url.toString(),\n      canonicalUrl: undefined,\n      couldBeIntercepted: false,\n      prerendered: false,\n      postponed: false,\n      staleTime: -1,\n    }\n  }\n}\n\nexport function createFetch(\n  url: URL,\n  headers: RequestHeaders,\n  fetchPriority: 'auto' | 'high' | 'low' | null,\n  signal?: AbortSignal\n) {\n  const fetchUrl = new URL(url)\n\n  // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n  // cache busting search param) from the request so they're\n  // maximally cacheable.\n  setCacheBustingSearchParam(fetchUrl, headers)\n\n  if (process.env.__NEXT_TEST_MODE && fetchPriority !== null) {\n    headers['Next-Test-Fetch-Priority'] = fetchPriority\n  }\n\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    headers['x-deployment-id'] = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  return fetch(fetchUrl, {\n    // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n    credentials: 'same-origin',\n    headers,\n    priority: fetchPriority || undefined,\n    signal,\n  })\n}\n\nexport function createFromNextReadableStream(\n  flightStream: ReadableStream<Uint8Array>\n): Promise<unknown> {\n  return createFromReadableStream(flightStream, {\n    callServer,\n    findSourceMapURL,\n  })\n}\n\nfunction createUnclosingPrefetchStream(\n  originalFlightStream: ReadableStream<Uint8Array>\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n"], "names": ["createFetch", "createFromNextReadableStream", "fetchServerResponse", "urlToUrlWithoutFlightMarker", "createFromReadableStream", "process", "env", "NEXT_RUNTIME", "require", "url", "urlWithoutFlightParameters", "URL", "location", "origin", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "length", "slice", "doMpaNavigation", "flightData", "toString", "canonicalUrl", "undefined", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "abortController", "AbortController", "window", "addEventListener", "abort", "options", "flightRouterState", "nextUrl", "prefetchKind", "headers", "RSC_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "prepareFlightRouterStateForRequest", "isHmrRefresh", "PrefetchKind", "AUTO", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_URL", "res", "fetchPriority", "TEMPORARY", "signal", "responseUrl", "redirected", "contentType", "get", "interception", "includes", "NEXT_DID_POSTPONE_HEADER", "staleTimeHeaderSeconds", "NEXT_ROUTER_STALE_TIME_HEADER", "parseInt", "isFlightResponse", "startsWith", "RSC_CONTENT_TYPE_HEADER", "ok", "body", "hash", "TURBOPACK", "waitForWebpackRuntimeHotUpdate", "flightStream", "createUnclosingPrefetchStream", "response", "getAppBuildId", "b", "normalizeFlightData", "f", "S", "err", "aborted", "console", "error", "fetchUrl", "setCacheBustingSearchParam", "__NEXT_TEST_MODE", "NEXT_DEPLOYMENT_ID", "fetch", "credentials", "priority", "callServer", "findSourceMapURL", "originalFlightStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;IA6QgBA,WAAW;eAAXA;;IA8BAC,4BAA4B;eAA5BA;;IApLMC,mBAAmB;eAAnBA;;IAlDNC,2BAA2B;eAA3BA;;;kCAxCT;+BACoB;qCACM;oCACJ;mCAKtB;4BACuB;4CACa;AArC3C,aAAa;AACb,6DAA6D;AAC7D,6EAA6E;AAC7E,MAAM,EAAEC,wBAAwB,EAAE,GAChC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AA2DP,SAASL,4BAA4BM,GAAW;IACrD,MAAMC,6BAA6B,IAAIC,IAAIF,KAAKG,SAASC,MAAM;IAC/DH,2BAA2BI,YAAY,CAACC,MAAM,CAACC,sCAAoB;IACnE,IAAIX,QAAQC,GAAG,CAACW,QAAQ,KAAK,cAAc;QACzC,IACEZ,QAAQC,GAAG,CAACY,oBAAoB,KAAK,YACrCR,2BAA2BS,QAAQ,CAACC,QAAQ,CAAC,SAC7C;YACA,MAAM,EAAED,QAAQ,EAAE,GAAGT;YACrB,MAAMW,SAASF,SAASC,QAAQ,CAAC,gBAAgB,KAAK;YACtD,gEAAgE;YAChEV,2BAA2BS,QAAQ,GAAGA,SAASG,KAAK,CAAC,GAAG,CAACD;QAC3D;IACF;IACA,OAAOX;AACT;AAEA,SAASa,gBAAgBd,GAAW;IAClC,OAAO;QACLe,YAAYrB,4BAA4BM,KAAKgB,QAAQ;QACrDC,cAAcC;QACdC,oBAAoB;QACpBC,aAAa;QACbC,WAAW;QACXC,WAAW,CAAC;IACd;AACF;AAEA,IAAIC,kBAAkB,IAAIC;AAE1B,IAAI,OAAOC,WAAW,aAAa;IACjC,sEAAsE;IACtE,8EAA8E;IAC9E,0EAA0E;IAC1E,YAAY;IACZA,OAAOC,gBAAgB,CAAC,YAAY;QAClCH,gBAAgBI,KAAK;IACvB;IAEA,8EAA8E;IAC9E,mEAAmE;IACnEF,OAAOC,gBAAgB,CAAC,YAAY;QAClCH,kBAAkB,IAAIC;IACxB;AACF;AAMO,eAAe/B,oBACpBO,GAAQ,EACR4B,OAAmC;IAEnC,MAAM,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGH;IAErD,MAAMI,UAA0B;QAC9B,yBAAyB;QACzB,CAACC,4BAAU,CAAC,EAAE;QACd,mCAAmC;QACnC,CAACC,+CAA6B,CAAC,EAAEC,IAAAA,qDAAkC,EACjEN,mBACAD,QAAQQ,YAAY;IAExB;IAEA;;;;;GAKC,GACD,IAAIL,iBAAiBM,gCAAY,CAACC,IAAI,EAAE;QACtCN,OAAO,CAACO,6CAA2B,CAAC,GAAG;IACzC;IAEA,IAAI3C,QAAQC,GAAG,CAACW,QAAQ,KAAK,iBAAiBoB,QAAQQ,YAAY,EAAE;QAClEJ,OAAO,CAACQ,yCAAuB,CAAC,GAAG;IACrC;IAEA,IAAIV,SAAS;QACXE,OAAO,CAACS,0BAAQ,CAAC,GAAGX;IACtB;IAEA,IAAI;YAoCqBY;QAnCvB,wHAAwH;QACxH,4HAA4H;QAC5H,kEAAkE;QAClE,yHAAyH;QACzH,MAAMC,gBAAgBZ,eAClBA,iBAAiBM,gCAAY,CAACO,SAAS,GACrC,SACA,QACF;QAEJ,IAAIhD,QAAQC,GAAG,CAACW,QAAQ,KAAK,cAAc;YACzC,IAAIZ,QAAQC,GAAG,CAACY,oBAAoB,KAAK,UAAU;gBACjD,oEAAoE;gBACpE,oEAAoE;gBACpE,kBAAkB;gBAClBT,MAAM,IAAIE,IAAIF;gBACd,IAAIA,IAAIU,QAAQ,CAACC,QAAQ,CAAC,MAAM;oBAC9BX,IAAIU,QAAQ,IAAI;gBAClB,OAAO;oBACLV,IAAIU,QAAQ,IAAI;gBAClB;YACF;QACF;QAEA,MAAMgC,MAAM,MAAMnD,YAChBS,KACAgC,SACAW,eACApB,gBAAgBsB,MAAM;QAGxB,MAAMC,cAAcpD,4BAA4BgD,IAAI1C,GAAG;QACvD,MAAMiB,eAAeyB,IAAIK,UAAU,GAAGD,cAAc5B;QAEpD,MAAM8B,cAAcN,IAAIV,OAAO,CAACiB,GAAG,CAAC,mBAAmB;QACvD,MAAMC,eAAe,CAAC,GAACR,mBAAAA,IAAIV,OAAO,CAACiB,GAAG,CAAC,4BAAhBP,iBAAyBS,QAAQ,CAACV,0BAAQ;QACjE,MAAMpB,YAAY,CAAC,CAACqB,IAAIV,OAAO,CAACiB,GAAG,CAACG,0CAAwB;QAC5D,MAAMC,yBAAyBX,IAAIV,OAAO,CAACiB,GAAG,CAC5CK,+CAA6B;QAE/B,MAAMhC,YACJ+B,2BAA2B,OACvBE,SAASF,wBAAwB,MAAM,OACvC,CAAC;QACP,IAAIG,mBAAmBR,YAAYS,UAAU,CAACC,yCAAuB;QAErE,IAAI9D,QAAQC,GAAG,CAACW,QAAQ,KAAK,cAAc;YACzC,IAAIZ,QAAQC,GAAG,CAACY,oBAAoB,KAAK,UAAU;gBACjD,IAAI,CAAC+C,kBAAkB;oBACrBA,mBAAmBR,YAAYS,UAAU,CAAC;gBAC5C;YACF;QACF;QAEA,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAACD,oBAAoB,CAACd,IAAIiB,EAAE,IAAI,CAACjB,IAAIkB,IAAI,EAAE;YAC7C,2FAA2F;YAC3F,IAAI5D,IAAI6D,IAAI,EAAE;gBACZf,YAAYe,IAAI,GAAG7D,IAAI6D,IAAI;YAC7B;YAEA,OAAO/C,gBAAgBgC,YAAY9B,QAAQ;QAC7C;QAEA,uEAAuE;QACvE,0DAA0D;QAC1D,wDAAwD;QACxD,oGAAoG;QACpG,IAAIpB,QAAQC,GAAG,CAACW,QAAQ,KAAK,gBAAgB,CAACZ,QAAQC,GAAG,CAACiE,SAAS,EAAE;YACnE,MAAM/D,QAAQ,gDAAgDgE,8BAA8B;QAC9F;QAEA,2EAA2E;QAC3E,MAAMC,eAAe3C,YACjB4C,8BAA8BvB,IAAIkB,IAAI,IACtClB,IAAIkB,IAAI;QACZ,MAAMM,WAAW,MAAO1E,6BACtBwE;QAGF,IAAIG,IAAAA,yBAAa,QAAOD,SAASE,CAAC,EAAE;YAClC,OAAOtD,gBAAgB4B,IAAI1C,GAAG;QAChC;QAEA,OAAO;YACLe,YAAYsD,IAAAA,sCAAmB,EAACH,SAASI,CAAC;YAC1CrD,cAAcA;YACdE,oBAAoB+B;YACpB9B,aAAa8C,SAASK,CAAC;YACvBlD;YACAC;QACF;IACF,EAAE,OAAOkD,KAAK;QACZ,IAAI,CAACjD,gBAAgBsB,MAAM,CAAC4B,OAAO,EAAE;YACnCC,QAAQC,KAAK,CACX,AAAC,qCAAkC3E,MAAI,yCACvCwE;QAEJ;QAEA,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YACLzD,YAAYf,IAAIgB,QAAQ;YACxBC,cAAcC;YACdC,oBAAoB;YACpBC,aAAa;YACbC,WAAW;YACXC,WAAW,CAAC;QACd;IACF;AACF;AAEO,SAAS/B,YACdS,GAAQ,EACRgC,OAAuB,EACvBW,aAA6C,EAC7CE,MAAoB;IAEpB,MAAM+B,WAAW,IAAI1E,IAAIF;IAEzB,6EAA6E;IAC7E,0DAA0D;IAC1D,uBAAuB;IACvB6E,IAAAA,sDAA0B,EAACD,UAAU5C;IAErC,IAAIpC,QAAQC,GAAG,CAACiF,gBAAgB,IAAInC,kBAAkB,MAAM;QAC1DX,OAAO,CAAC,2BAA2B,GAAGW;IACxC;IAEA,IAAI/C,QAAQC,GAAG,CAACkF,kBAAkB,EAAE;QAClC/C,OAAO,CAAC,kBAAkB,GAAGpC,QAAQC,GAAG,CAACkF,kBAAkB;IAC7D;IAEA,OAAOC,MAAMJ,UAAU;QACrB,wFAAwF;QACxFK,aAAa;QACbjD;QACAkD,UAAUvC,iBAAiBzB;QAC3B2B;IACF;AACF;AAEO,SAASrD,6BACdwE,YAAwC;IAExC,OAAOrE,yBAAyBqE,cAAc;QAC5CmB,YAAAA,yBAAU;QACVC,kBAAAA,qCAAgB;IAClB;AACF;AAEA,SAASnB,8BACPoB,oBAAgD;IAEhD,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,MAAMC,SAASD,qBAAqBE,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMN,OAAOO,IAAI;gBACzC,IAAI,CAACF,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWI,OAAO,CAACF;oBACnB;gBACF;gBACA,qEAAqE;gBACrE,qBAAqB;gBACrB;YACF;QACF;IACF;AACF"}